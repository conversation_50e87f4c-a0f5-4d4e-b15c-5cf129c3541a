/// <reference types="vite/client" />
/// <reference types="unplugin-icons/types/vue" />
/// <reference types="unplugin-vue-router/client" />


interface ImportMetaEnv {
  VITE_APP_TITLE: string
  VITE_APP_API_URL: string
  VITE_APP_ALG_API_URL: string

  VITE_APP_GUC_APPID: string
  VITE_APP_GUC_API_URL: string

  VITE_APP_ONLINE_MONITOR_URL: string
  VITE_APP_ONLINE_MONITOR_SERVICE: string
  VITE_APP_ONLINE_MONITOR_APPID: string

  /**
   * 是否是私有化部署
   */
  VITE_APP_PRIVATE_DEPLOY: boolean

  VITE_APP_PREVIEW_BASE: string
}
