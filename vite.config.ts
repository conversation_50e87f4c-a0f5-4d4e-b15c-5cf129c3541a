import path from 'node:path'
import process from 'node:process'
import { type ProxyOptions, type UserConfigExport, defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import jsx from '@vitejs/plugin-vue-jsx'
import imports from 'unplugin-auto-import/vite'
import components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import vueRouter from 'unplugin-vue-router/vite'
import IconsResolver from 'unplugin-icons/resolver'
import unocss from 'unocss/vite'
import svg from 'unplugin-svg-component/vite'
import inspect from 'vite-plugin-inspect'
import legacy from '@vitejs/plugin-legacy'

import { VITE_DYNAMIC_IMPORT_FUNC_NAME } from './src/env.vite'

const ENV_PREFIX = 'VITE_APP'

export default defineConfig(async ({ mode, command }) => {
  const isBuild = command === 'build'
  const devConfig = await getDevConfig(mode)
  const env = loadEnv(mode, process.cwd(), ENV_PREFIX) as any
  const proxy = (await getProxyConfig(env)) as unknown as Record<string, ProxyOptions>

  const conf: UserConfigExport = {
    base: './',
    envPrefix: ENV_PREFIX,

    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      proxy,
      port: 3000,
    },

    plugins: [
      // https://github.com/vitejs/vite/pull/10139
      legacy({
        renderModernChunks: false,
        renderLegacyChunks: true,
      }),

      // https://github.com/posva/unplugin-vue-router
      vueRouter({
        dts: 'types/typed-router.d.ts',
        exclude: ['**/components'],
      }),

      vue(),
      jsx(),

      // https://github.com/antfu/unplugin-auto-import
      imports({
        dts: 'types/generated/auto-imports.d.ts',
        imports: ['vue', 'vue-router'],
      }),

      // https://github.com/antfu/unplugin-vue-components
      components({
        dts: 'types/generated/auto-components.d.ts',
        dirs: ['src/components'],
        resolvers: [
          IconsResolver(),
          VantResolver({
            importStyle: true,
          }),
        ],
      }),

      // https://github.com/unocss/unocss
      unocss(),

      // https://github.com/Jevon617/unplugin-svg-component
      svg({
        svgSpriteDomId: '__mk_app_svg_sprites__',
        iconDir: 'src/assets/icons',
        dts: true,
        dtsDir: 'types/generated',
        componentName: 'UnSvgIcon',
        componentStyle: '',
        scanStrategy: 'text',
      }),

      // inspect(),
    ],

    define: {
      __BUILD_TIMESTAMP__: JSON.stringify(new Date().getTime()),
    },

    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },

    experimental: {
      renderBuiltUrl: (filename, type) => {
        if (type.hostType !== 'js') return

        return { runtime: `window.${VITE_DYNAMIC_IMPORT_FUNC_NAME}(${JSON.stringify(filename)})` }
      },
    },
  }

  return conf

  async function getProxyConfig(_env: any) {
    const proxyConf: Record<string, ProxyOptions> = {}

    if (typeof devConfig.proxy === 'object') {
      Object.entries(devConfig.proxy).forEach(([path, host]) => {
        proxyConf[path] = {
          target: host,
          changeOrigin: true,
          rewrite: (p) => p.slice(path.length),
        }
      })
    }

    return proxyConf
  }
})

async function getDevConfig(mode: string): Promise<DevConfig> {
  if (mode !== 'development') return { proxy: {} }

  try {
    const res = await import('./dev.config')
    return res.devConfig
  } catch (error) {
    console.log(
      error,
      '未检测到 dev.config.ts 文件，请根据 dev.config.ts.example 创建 dev.config.ts',
    )
    process.exit(0)
  }
}

export interface DevConfig {
  proxy: Record<string, string>
}
