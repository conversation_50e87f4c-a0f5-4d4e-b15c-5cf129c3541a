<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />

    <title>%VITE_APP_TITLE%</title>
    <link rel="icon" href="./favicon.ico" />
    <link rel="stylesheet" href="./resource/font_3320890_x76orinzo.css" />
    <script src="./resource/font_2429842_r7xxxvmc69.js"></script>
    <script src="./config.js"></script>
    <!-- <script src="resource/tinymce/js/tinymce/tinymce.min.js"></script> -->
  </head>
  <body>
    <div id="app">
      <style>
        .app-loading {
          display: flex;
          width: 100%;
          height: 100%;
          justify-content: center;
          align-items: center;
          flex-direction: column;
        }

        .app-loading .app-loading-wrap {
          position: absolute;
          top: 50%;
          left: 50%;
          display: flex;
          -webkit-transform: translate3d(-50%, -50%, 0);
          transform: translate3d(-50%, -60%, 0);
          justify-content: center;
          align-items: center;
          flex-direction: column;
        }

        .app-loading .app-loading-title {
          display: flex;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
          font-size: 24px;
          color: rgba(0, 0, 0, 0.8);
          justify-content: center;
          align-items: center;
        }

        .app-loading .app-loading-logo {
          display: block;
          height: 200px;
          margin: 0 auto;
          margin-bottom: 0;
          box-sizing: border-box;
        }
      </style>
      <div class="app-loading">
        <div class="app-loading-wrap">
          <div class="app-loading">
            <img class="app-loading-logo" src="./loading-index.gif" alt="Logo" />
          </div>
          <div class="app-loading-title">%VITE_APP_TITLE%</div>
        </div>
      </div>
    </div>

    <script type="module" src="src/main.ts"></script>
  </body>
</html>
