#!/usr/bin/env node

/**
 * 检查环境变量配置是否正确
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 需要检查的环境变量
const requiredEnvVars = [
  'VITE_APP_TITLE',
  'VITE_APP_API_URL',
  'VITE_APP_ALG_API_URL',
  'VITE_APP_GUC_APPID',
  'VITE_APP_GUC_API_URL',
  'VITE_APP_ONLINE_MONITOR_URL',
  'VITE_APP_ONLINE_MONITOR_SERVICE',
  'VITE_APP_ONLINE_MONITOR_APPID',
  'VITE_APP_PRIVATE_DEPLOY',
  'VITE_APP_PREVIEW_BASE'
]

function checkEnvFile(filePath) {
  console.log(`\n检查文件: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`)
    return false
  }

  const content = fs.readFileSync(filePath, 'utf-8')
  const lines = content.split('\n')
  const envVars = {}

  // 解析环境变量
  lines.forEach(line => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=')
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim()
        envVars[key.trim()] = value
      }
    }
  })

  // 检查必需的环境变量
  let allPresent = true
  requiredEnvVars.forEach(varName => {
    if (envVars.hasOwnProperty(varName)) {
      const value = envVars[varName]
      if (value === '' || value === '""' || value === "''") {
        console.log(`⚠️  ${varName}: 值为空`)
      } else {
        console.log(`✅ ${varName}: ${value}`)
      }
    } else {
      console.log(`❌ ${varName}: 缺失`)
      allPresent = false
    }
  })

  return allPresent
}

function checkTypeDefinitions() {
  console.log('\n检查 TypeScript 类型定义...')
  
  const typesFile = path.join(__dirname, '../types/env.d.ts')
  if (!fs.existsSync(typesFile)) {
    console.log('❌ types/env.d.ts 文件不存在')
    return false
  }

  const content = fs.readFileSync(typesFile, 'utf-8')
  let allPresent = true

  requiredEnvVars.forEach(varName => {
    if (content.includes(varName)) {
      console.log(`✅ ${varName}: 已定义`)
    } else {
      console.log(`❌ ${varName}: 类型定义缺失`)
      allPresent = false
    }
  })

  return allPresent
}

function checkViteConfig() {
  console.log('\n检查 Vite 配置...')
  
  const viteConfigFile = path.join(__dirname, '../vite.config.ts')
  if (!fs.existsSync(viteConfigFile)) {
    console.log('❌ vite.config.ts 文件不存在')
    return false
  }

  const content = fs.readFileSync(viteConfigFile, 'utf-8')
  
  if (content.includes("ENV_PREFIX = 'VITE_APP'")) {
    console.log('✅ Vite 环境变量前缀配置正确')
    return true
  } else {
    console.log('❌ Vite 环境变量前缀配置错误')
    return false
  }
}

function main() {
  console.log('🔍 检查环境变量配置...\n')

  const envFile = path.join(__dirname, '../.env')
  const envProdFile = path.join(__dirname, '../.env.production')

  const envOk = checkEnvFile(envFile)
  const envProdOk = checkEnvFile(envProdFile)
  const typesOk = checkTypeDefinitions()
  const viteConfigOk = checkViteConfig()

  console.log('\n📋 检查结果:')
  console.log(`- .env 文件: ${envOk ? '✅' : '❌'}`)
  console.log(`- .env.production 文件: ${envProdOk ? '✅' : '❌'}`)
  console.log(`- TypeScript 类型定义: ${typesOk ? '✅' : '❌'}`)
  console.log(`- Vite 配置: ${viteConfigOk ? '✅' : '❌'}`)

  if (envOk && envProdOk && typesOk && viteConfigOk) {
    console.log('\n🎉 所有环境变量配置检查通过！')
    process.exit(0)
  } else {
    console.log('\n❌ 环境变量配置存在问题，请检查上述错误')
    process.exit(1)
  }
}

main()
