import type { DevConfig } from './vite.config'

export const devConfig: DevConfig = {
  proxy: {
    '/api/monitor-collect': 'https://onlinemonitor-server-dev.geega.com/',
    '/storage/': 'https://pek-dev.geega.com/storage/',
    '/app-api/dev': 'http://intelli-matching-service.caas-cloud-dev.geega.com/',
    '/gateway/guc': 'https://guc3-api-test.geega.com/',
    '/file-preview/dev': 'http://kkvf-big-excel-view.caas-cloud-test.geega.com/',
    '/file-preview/test': 'http://kkvf-big-excel-view.caas-cloud-test.geega.com/',
  },
}
