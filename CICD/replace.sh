#!/bin/bash

# 应用标题
__VITE_APP_TITLE__=$VITE_APP_TITLE

# API 请求路径
__VITE_APP_API_URL__=$VITE_APP_API_URL

# 算法 API 请求路径
__VITE_APP_ALG_API_URL__=$VITE_APP_ALG_API_URL

# GUC 配置
__VITE_APP_GUC_APPID__=$VITE_APP_GUC_APPID
__VITE_APP_GUC_API_URL__=$VITE_APP_GUC_API_URL

# 在线监控配置
__VITE_APP_ONLINE_MONITOR_URL__=$VITE_APP_ONLINE_MONITOR_URL
__VITE_APP_ONLINE_MONITOR_SERVICE__=$VITE_APP_ONLINE_MONITOR_SERVICE
__VITE_APP_ONLINE_MONITOR_APPID__=$VITE_APP_ONLINE_MONITOR_APPID

# 是否是私有化部署
__VITE_APP_PRIVATE_DEPLOY__=$VITE_APP_PRIVATE_DEPLOY

# 预览服务基础地址
__VITE_APP_PREVIEW_BASE__=$VITE_APP_PREVIEW_BASE

# Minio 存储地址（如果需要）
__NGINX_MINIO_URL__=$NGINX_MINIO_URL

echo "正在替换环境变量..."

# 应用标题
if [ -n "${__VITE_APP_TITLE__}" ]; then
  echo "__VITE_APP_TITLE__: ${__VITE_APP_TITLE__}"
  sed -i "s|__VITE_APP_TITLE__|${__VITE_APP_TITLE__}|g" `grep __VITE_APP_TITLE__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# API 请求路径
if [ -n "${__VITE_APP_API_URL__}" ]; then
  echo "__VITE_APP_API_URL__: ${__VITE_APP_API_URL__}"
  sed -i "s|__VITE_APP_API_URL__|${__VITE_APP_API_URL__}|g" `grep __VITE_APP_API_URL__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# 算法 API 请求路径
if [ -n "${__VITE_APP_ALG_API_URL__}" ]; then
  echo "__VITE_APP_ALG_API_URL__: ${__VITE_APP_ALG_API_URL__}"
  sed -i "s|__VITE_APP_ALG_API_URL__|${__VITE_APP_ALG_API_URL__}|g" `grep __VITE_APP_ALG_API_URL__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# GUC 配置
if [ -n "${__VITE_APP_GUC_APPID__}" ]; then
  echo "__VITE_APP_GUC_APPID__: ${__VITE_APP_GUC_APPID__}"
  sed -i "s|__VITE_APP_GUC_APPID__|${__VITE_APP_GUC_APPID__}|g" `grep __VITE_APP_GUC_APPID__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

if [ -n "${__VITE_APP_GUC_API_URL__}" ]; then
  echo "__VITE_APP_GUC_API_URL__: ${__VITE_APP_GUC_API_URL__}"
  sed -i "s|__VITE_APP_GUC_API_URL__|${__VITE_APP_GUC_API_URL__}|g" `grep __VITE_APP_GUC_API_URL__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# 在线监控配置
if [ -n "${__VITE_APP_ONLINE_MONITOR_URL__}" ]; then
  echo "__VITE_APP_ONLINE_MONITOR_URL__: ${__VITE_APP_ONLINE_MONITOR_URL__}"
  sed -i "s|__VITE_APP_ONLINE_MONITOR_URL__|${__VITE_APP_ONLINE_MONITOR_URL__}|g" `grep __VITE_APP_ONLINE_MONITOR_URL__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

if [ -n "${__VITE_APP_ONLINE_MONITOR_SERVICE__}" ]; then
  echo "__VITE_APP_ONLINE_MONITOR_SERVICE__: ${__VITE_APP_ONLINE_MONITOR_SERVICE__}"
  sed -i "s|__VITE_APP_ONLINE_MONITOR_SERVICE__|${__VITE_APP_ONLINE_MONITOR_SERVICE__}|g" `grep __VITE_APP_ONLINE_MONITOR_SERVICE__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

if [ -n "${__VITE_APP_ONLINE_MONITOR_APPID__}" ]; then
  echo "__VITE_APP_ONLINE_MONITOR_APPID__: ${__VITE_APP_ONLINE_MONITOR_APPID__}"
  sed -i "s|__VITE_APP_ONLINE_MONITOR_APPID__|${__VITE_APP_ONLINE_MONITOR_APPID__}|g" `grep __VITE_APP_ONLINE_MONITOR_APPID__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# 是否是私有化部署
if [ -n "${__VITE_APP_PRIVATE_DEPLOY__}" ]; then
  echo "__VITE_APP_PRIVATE_DEPLOY__: ${__VITE_APP_PRIVATE_DEPLOY__}"
  sed -i "s|__VITE_APP_PRIVATE_DEPLOY__|${__VITE_APP_PRIVATE_DEPLOY__}|g" `grep __VITE_APP_PRIVATE_DEPLOY__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

# 预览服务基础地址
if [ -n "${__VITE_APP_PREVIEW_BASE__}" ]; then
  echo "__VITE_APP_PREVIEW_BASE__: ${__VITE_APP_PREVIEW_BASE__}"
  sed -i "s|__VITE_APP_PREVIEW_BASE__|${__VITE_APP_PREVIEW_BASE__}|g" `grep __VITE_APP_PREVIEW_BASE__ -rl /usr/share/nginx/html 2>/dev/null || echo ""`
fi

echo "正在替换 Nginx 配置..."

# 检查 nginx 配置文件位置
NGINX_CONF_FILE="/etc/nginx/conf.d/default.conf"

if [ ! -f "$NGINX_CONF_FILE" ]; then
  echo "❌ 找不到 nginx 配置文件: $NGINX_CONF_FILE"
  echo "查找可能的配置文件:"
  find /etc -name "*.conf" -type f 2>/dev/null | grep nginx | head -5
  exit 1
fi

if [ -n "$NGINX_CONF_FILE" ]; then
  echo "✅ 使用 nginx 配置文件: $NGINX_CONF_FILE"

  # 显示替换前的内容
  echo "替换前的配置:"
  grep -n "__NGINX_" "$NGINX_CONF_FILE" || echo "没有找到占位符"

  # 使用 VITE_APP_API_URL 作为 API 代理地址
  if [ -n "${__VITE_APP_API_URL__}" ]; then
    echo "Nginx API 代理: ${__VITE_APP_API_URL__}"
    sed -i "s|__NGINX_API_URL__|${__VITE_APP_API_URL__}|g" "$NGINX_CONF_FILE"
  fi

  # 使用 VITE_APP_GUC_API_URL 作为 GUC 代理地址
  if [ -n "${__VITE_APP_GUC_API_URL__}" ]; then
    echo "Nginx GUC 代理: ${__VITE_APP_GUC_API_URL__}"
    sed -i "s|__NGINX_GUC_URL__|${__VITE_APP_GUC_API_URL__}|g" "$NGINX_CONF_FILE"
  fi

  # 使用 VITE_APP_ONLINE_MONITOR_URL 作为监控平台代理地址
  if [ -n "${__VITE_APP_ONLINE_MONITOR_URL__}" ]; then
    echo "Nginx 监控代理: ${__VITE_APP_ONLINE_MONITOR_URL__}"
    sed -i "s|__NGINX_MONITOR_URL__|${__VITE_APP_ONLINE_MONITOR_URL__}|g" "$NGINX_CONF_FILE"
  fi

  # Minio 存储地址（如果需要单独配置）
  if [ -n "${__NGINX_MINIO_URL__}" ]; then
    echo "Nginx Minio 代理: ${__NGINX_MINIO_URL__}"
    sed -i "s|__NGINX_MINIO_URL__|${__NGINX_MINIO_URL__}|g" "$NGINX_CONF_FILE"
  fi

  # 显示替换后的内容
  echo "替换后的配置:"
  grep -n "proxy_pass" "$NGINX_CONF_FILE" | head -5
fi

echo "替换环境变量完成"

nginx -g "daemon off;"
