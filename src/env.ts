import { joinURL } from 'ufo'

export * from './env.vite'

/**
 * 是否是微前端的子应用
 */
export const IS_IN_MICRO_FRAMEWORK = window.__MICRO_APP_ENVIRONMENT__

export const MICRO_FRAMEWORK_BASE_URL = window.__MICRO_APP_PUBLIC_PATH__

export const APP_TITLE = import.meta.env.VITE_APP_TITLE
export const API_URL = import.meta.env.VITE_APP_API_URL
export const ALG_API_URL = import.meta.env.VITE_APP_ALG_API_URL
/**
 * 构建模式, vite 的 mode 参数, vite --mode test
 */
export const ENV_MODE = import.meta.env.MODE

export const IS_DEV = import.meta.env.DEV

// GUC 配置
export const GUC_APPID = import.meta.env.VITE_APP_GUC_APPID
export const GUC_API_URL = import.meta.env.VITE_APP_GUC_API_URL

// 在线监控配置
export const ONLINE_MONITOR_URL = import.meta.env.VITE_APP_ONLINE_MONITOR_URL
export const ONLINE_MONITOR_SERVICE = import.meta.env.VITE_APP_ONLINE_MONITOR_SERVICE
export const ONLINE_MONITOR_APPID = import.meta.env.VITE_APP_ONLINE_MONITOR_APPID

// 是否是私有化部署
export const PRIVATE_DEPLOY = import.meta.env.VITE_APP_PRIVATE_DEPLOY

// 预览服务基础地址
export const PREVIEW_BASE_URL = import.meta.env.VITE_APP_PREVIEW_BASE
/**
 * 运行时的 host
 *
 * 主要区别处理微应用时的 host
 */
export const RUNTIME_ORIGIN = getRuntimeOrigin()

/**
 * 运行时的 baseUrl
 *
 * 主要区别处理微应用
 */
export const BASE_URL = getBaseUrl()
// --------- utils

function getRuntimeOrigin() {
  const u = new URL(MICRO_FRAMEWORK_BASE_URL || location.href)
  return u.origin
}

function getBaseUrl() {
  if (IS_IN_MICRO_FRAMEWORK) {
    return MICRO_FRAMEWORK_BASE_URL
  }

  return joinURL(RUNTIME_ORIGIN, location.pathname)
}
