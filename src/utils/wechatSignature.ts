/**
 * 微信签名本地存储管理工具
 * 用于缓存微信JS-SDK签名，避免重复请求
 */

// 签名缓存数据结构
interface WechatSignatureCache {
  appId: string
  timestamp: string
  nonceStr: string
  signature: string
  url: string // 签名对应的URL
  expiresAt: number // 过期时间戳
  createdAt: number // 创建时间戳
}

// 签名缓存配置
const SIGNATURE_CACHE_CONFIG = {
  // 缓存key前缀
  CACHE_KEY_PREFIX: 'wechat_signature_',
  // 签名有效期（毫秒），微信签名实际有效期为7200秒（2小时）
  EXPIRES_IN: 7200 * 1000,
  // 提前刷新时间（毫秒），在过期前10分钟开始刷新，确保用户使用时签名仍然有效
  REFRESH_BEFORE: 10 * 60 * 1000,
}

/**
 * 微信签名缓存管理类
 */
export class WechatSignatureManager {
  /**
   * 生成缓存key
   * @param url 页面URL
   * @returns 缓存key
   */
  private getCacheKey(url: string): string {
    // 使用URL的hash作为key的一部分，确保不同页面的签名分开存储
    const urlHash = this.hashCode(url)
    return `${SIGNATURE_CACHE_CONFIG.CACHE_KEY_PREFIX}${urlHash}`
  }

  /**
   * 简单的字符串hash函数
   * @param str 字符串
   * @returns hash值
   */
  private hashCode(str: string): string {
    let hash = 0
    if (str.length === 0)
      return hash.toString()
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString()
  }

  /**
   * 获取缓存的签名数据
   * @param url 页面URL
   * @returns 缓存的签名数据，如果不存在或已过期则返回null
   */
  getCachedSignature(url: string): WechatSignatureCache | null {
    try {
      const cacheKey = this.getCacheKey(url)
      const cachedData = localStorage.getItem(cacheKey)

      if (!cachedData) {
        return null
      }

      const signature: WechatSignatureCache = JSON.parse(cachedData)
      const now = Date.now()

      // 检查是否过期
      if (now >= signature.expiresAt) {
        console.log('微信签名已过期，清除缓存')
        this.clearSignature(url)
        return null
      }

      // 检查URL是否匹配
      if (signature.url !== url) {
        console.log('微信签名URL不匹配，清除缓存')
        this.clearSignature(url)
        return null
      }

      console.log('使用缓存的微信签名', {
        url,
        expiresAt: new Date(signature.expiresAt).toLocaleString(),
        remainingTime: `${Math.round((signature.expiresAt - now) / 1000 / 60)}分钟`,
      })

      return signature
    }
    catch (error) {
      console.error('获取缓存签名失败:', error)
      this.clearSignature(url)
      return null
    }
  }

  /**
   * 缓存签名数据
   * @param url 页面URL
   * @param signatureData 签名数据
   */
  cacheSignature(url: string, signatureData: {
    appId: string
    timestamp: string
    nonceStr: string
    signature: string
  }): void {
    try {
      const now = Date.now()
      const cacheData: WechatSignatureCache = {
        ...signatureData,
        url,
        expiresAt: now + SIGNATURE_CACHE_CONFIG.EXPIRES_IN,
        createdAt: now,
      }

      const cacheKey = this.getCacheKey(url)
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))

      console.log('微信签名已缓存', {
        url,
        expiresAt: new Date(cacheData.expiresAt).toLocaleString(),
        validFor: `${Math.round(SIGNATURE_CACHE_CONFIG.EXPIRES_IN / 1000 / 60)}分钟`,
      })
    }
    catch (error) {
      console.error('缓存签名失败:', error)
    }
  }

  /**
   * 获取签名剩余有效时间（分钟）
   * @param url 页面URL
   * @returns 剩余时间（分钟），如果没有缓存返回0
   */
  getRemainingTime(url: string): number {
    const cached = this.getCachedSignature(url)
    if (!cached) {
      return 0
    }

    const now = Date.now()
    const remainingMs = cached.expiresAt - now
    return Math.max(0, Math.round(remainingMs / 1000 / 60))
  }

  /**
   * 检查签名是否需要刷新
   * @param url 页面URL
   * @returns 是否需要刷新
   */
  shouldRefreshSignature(url: string): boolean {
    const cached = this.getCachedSignature(url)
    if (!cached) {
      return true
    }

    const now = Date.now()
    const timeUntilExpiry = cached.expiresAt - now

    // 如果距离过期时间小于刷新提前时间，则需要刷新
    const shouldRefresh = timeUntilExpiry <= SIGNATURE_CACHE_CONFIG.REFRESH_BEFORE

    if (shouldRefresh) {
      console.log('微信签名即将过期，需要刷新', {
        url,
        remainingTime: `${Math.round(timeUntilExpiry / 1000 / 60)}分钟`,
        refreshThreshold: `${SIGNATURE_CACHE_CONFIG.REFRESH_BEFORE / 1000 / 60}分钟`,
      })
    }

    return shouldRefresh
  }

  /**
   * 检查并清理过期的签名缓存
   */
  cleanupExpiredSignatures(): void {
    try {
      const keys = Object.keys(localStorage)
      const signatureKeys = keys.filter(key => key.startsWith(SIGNATURE_CACHE_CONFIG.CACHE_KEY_PREFIX))
      const now = Date.now()
      let cleanedCount = 0

      signatureKeys.forEach((key) => {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          if (!data.expiresAt || now >= data.expiresAt) {
            localStorage.removeItem(key)
            cleanedCount++
          }
        }
        catch {
          // 数据格式错误，直接删除
          localStorage.removeItem(key)
          cleanedCount++
        }
      })

      if (cleanedCount > 0) {
        console.log(`已清理${cleanedCount}个过期的微信签名缓存`)
      }
    }
    catch (error) {
      console.error('清理过期签名缓存失败:', error)
    }
  }

  /**
   * 清除指定URL的签名缓存
   * @param url 页面URL
   */
  clearSignature(url: string): void {
    try {
      const cacheKey = this.getCacheKey(url)
      localStorage.removeItem(cacheKey)
      console.log('已清除微信签名缓存:', url)
    }
    catch (error) {
      console.error('清除签名缓存失败:', error)
    }
  }

  /**
   * 清除所有签名缓存
   */
  clearAllSignatures(): void {
    try {
      const keys = Object.keys(localStorage)
      const signatureKeys = keys.filter(key => key.startsWith(SIGNATURE_CACHE_CONFIG.CACHE_KEY_PREFIX))

      signatureKeys.forEach((key) => {
        localStorage.removeItem(key)
      })

      console.log(`已清除所有微信签名缓存，共${signatureKeys.length}个`)
    }
    catch (error) {
      console.error('清除所有签名缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalCount: number
    validCount: number
    expiredCount: number
  } {
    try {
      const keys = Object.keys(localStorage)
      const signatureKeys = keys.filter(key => key.startsWith(SIGNATURE_CACHE_CONFIG.CACHE_KEY_PREFIX))
      const now = Date.now()

      let validCount = 0
      let expiredCount = 0

      signatureKeys.forEach((key) => {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          if (data.expiresAt && now < data.expiresAt) {
            validCount++
          }
          else {
            expiredCount++
          }
        }
        catch {
          expiredCount++
        }
      })

      return {
        totalCount: signatureKeys.length,
        validCount,
        expiredCount,
      }
    }
    catch (error) {
      console.error('获取缓存统计失败:', error)
      return { totalCount: 0, validCount: 0, expiredCount: 0 }
    }
  }
}

// 创建单例实例
export const wechatSignatureManager = new WechatSignatureManager()

// 导出便捷方法
export const getCachedWechatSignature = (url: string) => wechatSignatureManager.getCachedSignature(url)
export const cacheWechatSignature = (url: string, signature: any) => wechatSignatureManager.cacheSignature(url, signature)
export const shouldRefreshWechatSignature = (url: string) => wechatSignatureManager.shouldRefreshSignature(url)
export const clearWechatSignature = (url: string) => wechatSignatureManager.clearSignature(url)
export const clearAllWechatSignatures = () => wechatSignatureManager.clearAllSignatures()
