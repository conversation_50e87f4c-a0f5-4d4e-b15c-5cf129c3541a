import { domainConfig } from '@/integration/domain'

/**
 * 获取当前基地的环境标识
 */
export function getCurrentEnvironment(): string {
  const currentHost = domainConfig.value.host

  if (currentHost === '/app-api/dev') {
    return 'dev'
  }
  else if (currentHost === '/app-api/test') {
    return 'test'
  }

  return 'test' // 默认测试环境
}

/**
 * 创建基于当前基地的文件预览URL
 * @param filePath 文件路径
 * @returns 根据当前基地动态生成的文件预览URL
 */
export function createFilePreviewUrl(filePath: string): string {
  const env = getCurrentEnvironment()
  // 移除开头的斜杠（如果存在）
  const cleanPath = filePath.replace(/^\//, '')

  // 根据环境生成对应的代理路径
  return `/file-preview/${env}/${cleanPath}`
}

/**
 * 兼容旧版本的代理函数
 * @param path 文件路径
 * @returns 文件预览URL
 */
export function proxyFilePreview(path: string): string {
  return createFilePreviewUrl(path)
}
