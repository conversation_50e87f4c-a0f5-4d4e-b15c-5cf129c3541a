import { useLocalStorage } from '@vueuse/core'
import { showToast } from 'vant'

const enabledDevtools = useLocalStorage('enabled-devtools', false)

init()

function init() {
  if (enabledDevtools.value) {
    enableDevtools()
  }
}

async function enableDevtools() {
  const eruda = (await import('eruda')).default
  eruda.init()
}

async function disableDevtools() {
  const eruda = (await import('eruda')).default
  eruda.destroy()
}

export async function toggleDevtools(enabled?: boolean) {
  if (enabled == null) {
    enabled = !enabledDevtools.value
  }

  if (enabled) {
    await enableDevtools()
    showToast('Enable Devtools')
  } else {
    await disableDevtools()
    showToast('Disable Devtools')
  }

  enabledDevtools.value = enabled
}
