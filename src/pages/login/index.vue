<script lang="ts" setup>
import { LoginType } from '@/integration/guc'
import LoginForm from './components/LoginForm.vue'
import PickDomain from './components/PickDomain.vue'
import { loginStorage } from './components/baseConfig'

const router = useRouter()

const loginForm = ref<InstanceType<typeof LoginForm>>()

const loginFormState = reactive({
  type: LoginType.Password,
  visible: false,
})

const lastLoginType = computed(() => {
  return loginStorage.value.lastLoginType
})

async function showLoginForm(type: LoginType) {
  loginFormState.type = type
  loginFormState.visible = true

  await nextTick() // Ensure login form exists.

  loginForm.value?.initLoginForm()
}

async function onLoginSuccessful() {
  router.push('/home')
}
</script>

<template>
  <div class="login-wrap">
    <div class="app-name">
      <div class="app-name-english">&nbsp;</div>
      <div class="app-name-china">智工育匠</div>
    </div>
    <div class="login-type-select-wrap">
      <PickDomain />
      <div class="btn-wrap-relative">
        <van-button color="#00996B" block @click.stop="showLoginForm(LoginType.Domain)">
          <div class="btn-inner-wrap">
            <div class="btn-inner-icon">
              <img src="../../assets/img/domain-login-icon.png" />
            </div>
            域账号登录
          </div>
        </van-button>
        <img
          v-if="lastLoginType === LoginType.Domain"
          class="login-type-icon"
          src="../../assets/img/login-type.png"
        />
      </div>
      <div class="change-other-login-type">
        <span>更换其它登录方式</span>
      </div>
      <div class="btn-wrap btn-wrap-relative">
        <van-button color="#E8E9EA" block @click.stop="showLoginForm(LoginType.Password)">
          <div class="btn-inner-wrap gray-btn">
            <div class="btn-inner-icon">
              <img src="../../assets/img/password-login-icon.png" />
            </div>
            密码登录
          </div>
        </van-button>
        <img
          v-if="lastLoginType === LoginType.Password"
          class="login-type-icon"
          src="../../assets/img/login-type.png"
        />
      </div>
      <div class="btn-wrap btn-wrap-relative" v-if="false">
        <van-button color="#E8E9EA" block @click.stop="showLoginForm(LoginType.Password)">
          <div class="btn-inner-wrap gray-btn">
            <div class="btn-inner-icon">
              <img src="../../assets/img/note-login-icon.png" />
            </div>
            短信登录
          </div>
        </van-button>
        <img
          v-if="lastLoginType === LoginType.Password"
          class="login-type-icon"
          src="../../assets/img/login-type.png"
        />
      </div>
    </div>
    <van-action-sheet v-model:show="loginFormState.visible">
      <div class="panel-content" @click.stop>
        <div class="close-btn">
          <van-icon
            color="#333333"
            name="cross"
            size="20px"
            @click="loginFormState.visible = false"
          />
        </div>
        <LoginForm ref="loginForm" :type="loginFormState.type" @success="onLoginSuccessful" />
      </div>
    </van-action-sheet>
  </div>
</template>

<style lang="less" scoped>
.login-wrap {
  width: 100%;
  height: 100vh;
  background-image: url('../../assets/img/login-background.png');
  background-repeat: no-repeat;
  background-color: #ffffff;
  background-size: cover;
  position: relative;
  .app-name {
    position: absolute;
    padding-left: 2rem;
    box-sizing: border-box;
    top: 32px;
    height: 100px;
    color: #333333;
    font-family: 'OPPOSans';
    font-style: normal;
    &-english {
      font-size: 18px;
      font-weight: 400;
      color: #94989e;
    }
    &-china {
      font-size: 24px;
      font-weight: 700;
      padding-top: 10px;
      color: #5b5f67;
    }
  }
  .login-type-select-wrap {
    width: 100%;
    position: absolute;
    bottom: 75px;
    height: 330px;
    padding: 0 2rem;
    box-sizing: border-box;
    .btn-wrap-relative {
      position: relative;
      .login-type-icon {
        width: 66px;
        position: absolute;
        right: -4px;
        top: 12px;
        z-index: 100;
      }
      &.btn-wrap {
        padding-bottom: 25px;
      }
    }
    .btn-inner-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      &.gray-btn {
        color: #000000;
      }
      .btn-inner-icon {
        width: 22px;
        margin-right: 5px;
        > img {
          width: 22px;
          height: 22px;
          vertical-align: bottom;
        }
      }
    }

    .change-other-login-type {
      height: 55px;
      line-height: 55px;
      color: #999999;
      font-size: 12px;
      font-style: normal;
      display: flex;
      justify-content: center;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: calc(50% - 70px);
        left: 0;
        top: 27px;
        height: 1px;
        background: rgba(0, 0, 0, 0.06);
      }
      &::after {
        position: absolute;
        content: '';
        width: calc(50% - 70px);
        right: 0;
        top: 27px;
        height: 1px;
        background: rgba(0, 0, 0, 0.06);
      }
    }
  }
  :deep(.van-overlay) {
    background-color: rgba(0, 0, 0, 0.2) !important;
  }
  .panel-content {
    height: 610px;
    background-color: #eaeeef;
    .close-btn {
      padding: 12px 15px;
      display: flex;
      justify-content: right;
    }
  }
}
</style>
