// This file was automatically generated by `scripts/gen-api.ts`, Please do not edit it directly!
/* tslint:disable */
import type { APIModels } from './api.model';
import { httpAdaptor } from './api.adaptor';
/**
* 开始学习
*
* url path: /v1/manage/user-learnings/{learningDetailId}/start
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsLearningDetailIdStartPut(params: APIModels["V1ManageUserLearningsLearningDetailIdStartPutParam"]) {
  return httpAdaptor.put<APIModels["V1ManageUserLearningsLearningDetailIdStartPutResponse"]>({
    url: `/v1/manage/user-learnings/${params.learningDetailId}/start`,
    param: params,
  });
}

/**
* 增加用户学习时间
*
* url path: /v1/manage/user-learnings/increment-time
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsIncrementTimePut(data: APIModels["V1ManageUserLearningsIncrementTimePutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageUserLearningsIncrementTimePutResponse"]>({
    url: `/v1/manage/user-learnings/increment-time`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/tran-project
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectPut(data: APIModels["V1ManageTranProjectPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageTranProjectPutResponse"]>({
    url: `/v1/manage/tran-project`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/tran-project
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectPost(data: APIModels["V1ManageTranProjectPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTranProjectPostResponse"]>({
    url: `/v1/manage/tran-project`,
    data: data,
  });
}

/**
* 修改训练预约
*
* url path: /v1/manage/train-bookings
* @tags 虚拟班组长-训练预约配置
*/
export function V1ManageTrainBookingsPut(data: APIModels["V1ManageTrainBookingsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageTrainBookingsPutResponse"]>({
    url: `/v1/manage/train-bookings`,
    data: data,
  });
}

/**
* 新增训练预约
*
* url path: /v1/manage/train-bookings
* @tags 虚拟班组长-训练预约配置
*/
export function V1ManageTrainBookingsPost(data: APIModels["V1ManageTrainBookingsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTrainBookingsPostResponse"]>({
    url: `/v1/manage/train-bookings`,
    data: data,
  });
}

/**
* 修改工号
*
* url path: /v1/manage/sys-user/workNo
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserWorkNoPut(data: APIModels["V1ManageSysUserWorkNoPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSysUserWorkNoPutResponse"]>({
    url: `/v1/manage/sys-user/workNo`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/sys-unit
* @tags 管理端-组织机构(工位)管理
*/
export function V1ManageSysUnitPut(data: APIModels["V1ManageSysUnitPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSysUnitPutResponse"]>({
    url: `/v1/manage/sys-unit`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/sys-unit
* @tags 管理端-组织机构(工位)管理
*/
export function V1ManageSysUnitPost(data: APIModels["V1ManageSysUnitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUnitPostResponse"]>({
    url: `/v1/manage/sys-unit`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/sys-org
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgPut(data: APIModels["V1ManageSysOrgPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSysOrgPutResponse"]>({
    url: `/v1/manage/sys-org`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/sys-org
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgPost(data: APIModels["V1ManageSysOrgPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysOrgPostResponse"]>({
    url: `/v1/manage/sys-org`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/sys-config
* @tags 管理端-系统配置管理
*/
export function V1ManageSysConfigPut(data: APIModels["V1ManageSysConfigPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSysConfigPutResponse"]>({
    url: `/v1/manage/sys-config`,
    data: data,
  });
}

/**
* 修改场景
*
* url path: /v1/manage/scenes
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesPut(data: APIModels["V1ManageScenesPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageScenesPutResponse"]>({
    url: `/v1/manage/scenes`,
    data: data,
  });
}

/**
* 新增场景
*
* url path: /v1/manage/scenes
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesPost(data: APIModels["V1ManageScenesPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageScenesPostResponse"]>({
    url: `/v1/manage/scenes`,
    data: data,
  });
}

/**
* 修改场景题目
*
* url path: /v1/manage/scene-questions
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsPut(data: APIModels["V1ManageSceneQuestionsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSceneQuestionsPutResponse"]>({
    url: `/v1/manage/scene-questions`,
    data: data,
  });
}

/**
* 新增场景题目
*
* url path: /v1/manage/scene-questions
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsPost(data: APIModels["V1ManageSceneQuestionsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneQuestionsPostResponse"]>({
    url: `/v1/manage/scene-questions`,
    data: data,
  });
}

/**
* 修改场景线索
*
* url path: /v1/manage/scene-clues
* @tags 虚拟班组长-场景线索配置
*/
export function V1ManageSceneCluesPut(data: APIModels["V1ManageSceneCluesPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageSceneCluesPutResponse"]>({
    url: `/v1/manage/scene-clues`,
    data: data,
  });
}

/**
* 新增场景线索
*
* url path: /v1/manage/scene-clues
* @tags 虚拟班组长-场景线索配置
*/
export function V1ManageSceneCluesPost(data: APIModels["V1ManageSceneCluesPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneCluesPostResponse"]>({
    url: `/v1/manage/scene-clues`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/process-algorithm
* @tags 管理端-工艺算法管理
*/
export function V1ManageProcessAlgorithmPut(data: APIModels["V1ManageProcessAlgorithmPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageProcessAlgorithmPutResponse"]>({
    url: `/v1/manage/process-algorithm`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/process-algorithm
* @tags 管理端-工艺算法管理
*/
export function V1ManageProcessAlgorithmPost(data: APIModels["V1ManageProcessAlgorithmPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageProcessAlgorithmPostResponse"]>({
    url: `/v1/manage/process-algorithm`,
    data: data,
  });
}

/**
* 修改学习项目
*
* url path: /v1/manage/learning-projects
* @tags 管理端-学习项目配置
*/
export function V1ManageLearningProjectsPut(data: APIModels["V1ManageLearningProjectsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageLearningProjectsPutResponse"]>({
    url: `/v1/manage/learning-projects`,
    data: data,
  });
}

/**
* 新增学习项目
*
* url path: /v1/manage/learning-projects
* @tags 管理端-学习项目配置
*/
export function V1ManageLearningProjectsPost(data: APIModels["V1ManageLearningProjectsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLearningProjectsPostResponse"]>({
    url: `/v1/manage/learning-projects`,
    data: data,
  });
}

/**
* 修改学习材料
*
* url path: /v1/manage/learning-materials
* @tags 管理端-学习材料配置
*/
export function V1ManageLearningMaterialsPut(data: APIModels["V1ManageLearningMaterialsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageLearningMaterialsPutResponse"]>({
    url: `/v1/manage/learning-materials`,
    data: data,
  });
}

/**
* 新增学习材料
*
* url path: /v1/manage/learning-materials
* @tags 管理端-学习材料配置
*/
export function V1ManageLearningMaterialsPost(data: APIModels["V1ManageLearningMaterialsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLearningMaterialsPostResponse"]>({
    url: `/v1/manage/learning-materials`,
    data: data,
  });
}

/**
* 修改岗位
*
* url path: /v1/manage/jobs
* @tags 管理端-岗位配置
*/
export function V1ManageJobsPut(data: APIModels["V1ManageJobsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageJobsPutResponse"]>({
    url: `/v1/manage/jobs`,
    data: data,
  });
}

/**
* 新增岗位
*
* url path: /v1/manage/jobs
* @tags 管理端-岗位配置
*/
export function V1ManageJobsPost(data: APIModels["V1ManageJobsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageJobsPostResponse"]>({
    url: `/v1/manage/jobs`,
    data: data,
  });
}

/**
* 修改岗级
*
* url path: /v1/manage/job-levels
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsPut(data: APIModels["V1ManageJobLevelsPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageJobLevelsPutResponse"]>({
    url: `/v1/manage/job-levels`,
    data: data,
  });
}

/**
* 新增岗级
*
* url path: /v1/manage/job-levels
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsPost(data: APIModels["V1ManageJobLevelsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageJobLevelsPostResponse"]>({
    url: `/v1/manage/job-levels`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/exam-paper
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperPut(data: APIModels["V1ManageExamPaperPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageExamPaperPutResponse"]>({
    url: `/v1/manage/exam-paper`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/exam-paper
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperPost(data: APIModels["V1ManageExamPaperPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperPostResponse"]>({
    url: `/v1/manage/exam-paper`,
    data: data,
  });
}

/**
* 新增
*
* url path: /v1/manage/exam-paper-question
* @tags 管理端-试卷试题管理
*/
export function V1ManageExamPaperQuestionPut(data: APIModels["V1ManageExamPaperQuestionPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageExamPaperQuestionPutResponse"]>({
    url: `/v1/manage/exam-paper-question`,
    data: data,
  });
}

/**
* 修改字典数据
*
* url path: /v1/manage/dict-data
* @tags 管理端-系统字典数据管理
*/
export function V1ManageDictDataPut(data: APIModels["V1ManageDictDataPutRequestBody"]) {
  return httpAdaptor.put<APIModels["V1ManageDictDataPutResponse"]>({
    url: `/v1/manage/dict-data`,
    data: data,
  });
}

/**
* 新增字典数据
*
* url path: /v1/manage/dict-data
* @tags 管理端-系统字典数据管理
*/
export function V1ManageDictDataPost(data: APIModels["V1ManageDictDataPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageDictDataPostResponse"]>({
    url: `/v1/manage/dict-data`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/wxJsSdk
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginWxJsSdkPost(data: APIModels["V1OpenApiUserLoginWxJsSdkPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1OpenApiUserLoginWxJsSdkPostResponse"]>({
    url: `/v1/open-api/user-login/wxJsSdk`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/wechat/signature
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginWechatSignaturePost(data: APIModels["V1OpenApiUserLoginWechatSignaturePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1OpenApiUserLoginWechatSignaturePostResponse"]>({
    url: `/v1/open-api/user-login/wechat/signature`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/guc
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginGucPost(data: APIModels["V1OpenApiUserLoginGucPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1OpenApiUserLoginGucPostResponse"]>({
    url: `/v1/open-api/user-login/guc`,
    data: data,
  });
}

/**
* 完成训练接口
*
* url path: /v1/mobile/study/over
* @tags 移动端-考试页控制器
*/
export function V1MobileStudyOverPost(data: APIModels["V1MobileStudyOverPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1MobileStudyOverPostResponse"]>({
    url: `/v1/mobile/study/over`,
    data: data,
  });
}

/**
* 开始训练接口
*
* url path: /v1/mobile/study/exam-record/start
* @tags 移动端-考试页控制器
*/
export function V1MobileStudyExamRecordStartPost(data: APIModels["V1MobileStudyExamRecordStartPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1MobileStudyExamRecordStartPostResponse"]>({
    url: `/v1/mobile/study/exam-record/start`,
    data: data,
  });
}

/**
* 考试记录分页查询
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomePagePost(data: APIModels["V1MobileHomePagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1MobileHomePagePostResponse"]>({
    url: `/v1/mobile/home/<USER>
    data: data,
  });
}

/**
* 考试记录集合查询
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomeListPost(data: APIModels["V1MobileHomeListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1MobileHomeListPostResponse"]>({
    url: `/v1/mobile/home/<USER>
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/vehicle/page
* @tags 管理端-车型管理
*/
export function V1ManageVehiclePagePost(data: APIModels["V1ManageVehiclePagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageVehiclePagePostResponse"]>({
    url: `/v1/manage/vehicle/page`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/manage/vehicle/import
* @tags 管理端-车型管理
*/
export function V1ManageVehicleImportPost(data: APIModels["V1ManageVehicleImportPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageVehicleImportPostResponse"]>({
    url: `/v1/manage/vehicle/import`,
    data: data,
  });
}

/**
* 分页查询用户学习记录
*
* url path: /v1/manage/user-learnings/page
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsPagePost(data: APIModels["V1ManageUserLearningsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageUserLearningsPagePostResponse"]>({
    url: `/v1/manage/user-learnings/page`,
    data: data,
  });
}

/**
* 结束学习
*
* url path: /v1/manage/user-learnings/over
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsOverPost(data: APIModels["V1ManageUserLearningsOverPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageUserLearningsOverPostResponse"]>({
    url: `/v1/manage/user-learnings/over`,
    data: data,
  });
}

/**
* 启用/停用
*
* url path: /v1/manage/tran-project/status
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectStatusPost(data: APIModels["V1ManageTranProjectStatusPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTranProjectStatusPostResponse"]>({
    url: `/v1/manage/tran-project/status`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/tran-project/page
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectPagePost(data: APIModels["V1ManageTranProjectPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTranProjectPagePostResponse"]>({
    url: `/v1/manage/tran-project/page`,
    data: data,
  });
}

/**
* 项目集合
*
* url path: /v1/manage/tran-project/list
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectListPost(data: APIModels["V1ManageTranProjectListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTranProjectListPostResponse"]>({
    url: `/v1/manage/tran-project/list`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/train-study-records/page
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsPagePost(data: APIModels["V1ManageTrainStudyRecordsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTrainStudyRecordsPagePostResponse"]>({
    url: `/v1/manage/train-study-records/page`,
    data: data,
  });
}

/**
* 分页查询不合格记录
*
* url path: /v1/manage/train-study-records/alarm-details/page
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsAlarmDetailsPagePost(data: APIModels["V1ManageTrainStudyRecordsAlarmDetailsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTrainStudyRecordsAlarmDetailsPagePostResponse"]>({
    url: `/v1/manage/train-study-records/alarm-details/page`,
    data: data,
  });
}

/**
* 不合格记录统计查询
*
* url path: /v1/manage/train-study-records/alarm-details-statistics
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsAlarmDetailsStatisticsPost(data: APIModels["V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTrainStudyRecordsAlarmDetailsStatisticsPostResponse"]>({
    url: `/v1/manage/train-study-records/alarm-details-statistics`,
    data: data,
  });
}

/**
* 查询训练预约列表
*
* url path: /v1/manage/train-bookings/list
* @tags 虚拟班组长-训练预约配置
*/
export function V1ManageTrainBookingsListPost(data: APIModels["V1ManageTrainBookingsListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageTrainBookingsListPostResponse"]>({
    url: `/v1/manage/train-bookings/list`,
    data: data,
  });
}

/**
* 修改
*
* url path: /v1/manage/sys-user/update
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserUpdatePost(data: APIModels["V1ManageSysUserUpdatePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserUpdatePostResponse"]>({
    url: `/v1/manage/sys-user/update`,
    data: data,
  });
}

/**
* 用户解除卡号绑定接口
*
* url path: /v1/manage/sys-user/unbindCard
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserUnbindCardPost(data: APIModels["V1ManageSysUserUnbindCardPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserUnbindCardPostResponse"]>({
    url: `/v1/manage/sys-user/unbindCard`,
    data: data,
  });
}

/**
* 启用/停用
*
* url path: /v1/manage/sys-user/status
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserStatusPost(data: APIModels["V1ManageSysUserStatusPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserStatusPostResponse"]>({
    url: `/v1/manage/sys-user/status`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/sys-user/page
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserPagePost(data: APIModels["V1ManageSysUserPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserPagePostResponse"]>({
    url: `/v1/manage/sys-user/page`,
    data: data,
  });
}

/**
* 分页查询用户-岗位数据
*
* url path: /v1/manage/sys-user/job-page
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserJobPagePost(data: APIModels["V1ManageSysUserJobPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserJobPagePostResponse"]>({
    url: `/v1/manage/sys-user/job-page`,
    data: data,
  });
}

/**
* 批量和单独为一个接口
*
* url path: /v1/manage/sys-user/delete-batch
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserDeleteBatchPost(data: APIModels["V1ManageSysUserDeleteBatchPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserDeleteBatchPostResponse"]>({
    url: `/v1/manage/sys-user/delete-batch`,
    data: data,
  });
}

/**
* 用户刷卡绑定卡号接口
*
* url path: /v1/manage/sys-user/bindCard
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserBindCardPost(data: APIModels["V1ManageSysUserBindCardPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysUserBindCardPostResponse"]>({
    url: `/v1/manage/sys-user/bindCard`,
    data: data,
  });
}

/**
* 树
*
* url path: /v1/manage/sys-unit/tree
* @tags 管理端-组织机构(工位)管理
*/
export function V1ManageSysUnitTreePost() {
  return httpAdaptor.post<APIModels["V1ManageSysUnitTreePostResponse"]>({
    url: `/v1/manage/sys-unit/tree`,
  });
}

/**
* 用户组织列表批量接口
*
* url path: /v1/manage/sys-org/userList
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgUserListPost(data: APIModels["V1ManageSysOrgUserListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSysOrgUserListPostResponse"]>({
    url: `/v1/manage/sys-org/userList`,
    data: data,
  });
}

/**
* 组织用户树
*
* url path: /v1/manage/sys-org/unit-user/tree
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgUnitUserTreePost() {
  return httpAdaptor.post<APIModels["V1ManageSysOrgUnitUserTreePostResponse"]>({
    url: `/v1/manage/sys-org/unit-user/tree`,
  });
}

/**
* 训练次数统计-分页查询接口
*
* url path: /v1/manage/statistical-report/trainNum
* @tags 数据统计报表
*/
export function V1ManageStatisticalReportTrainNumPost(data: APIModels["V1ManageStatisticalReportTrainNumPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageStatisticalReportTrainNumPostResponse"]>({
    url: `/v1/manage/statistical-report/trainNum`,
    data: data,
  });
}

/**
* 项目训练记录报表-分页查询接口
*
* url path: /v1/manage/statistical-report/recordDetail
* @tags 数据统计报表
*/
export function V1ManageStatisticalReportRecordDetailPost(data: APIModels["V1ManageStatisticalReportRecordDetailPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageStatisticalReportRecordDetailPostResponse"]>({
    url: `/v1/manage/statistical-report/recordDetail`,
    data: data,
  });
}

/**
* 项目统计报表-分页查询接口
*
* url path: /v1/manage/statistical-report/project
* @tags 数据统计报表
*/
export function V1ManageStatisticalReportProjectPost(data: APIModels["V1ManageStatisticalReportProjectPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageStatisticalReportProjectPostResponse"]>({
    url: `/v1/manage/statistical-report/project`,
    data: data,
  });
}

/**
* 项目人员统计-分页查询接口
*
* url path: /v1/manage/statistical-report/projectUser
* @tags 数据统计报表
*/
export function V1ManageStatisticalReportProjectUserPost(data: APIModels["V1ManageStatisticalReportProjectUserPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageStatisticalReportProjectUserPostResponse"]>({
    url: `/v1/manage/statistical-report/projectUser`,
    data: data,
  });
}

/**
* 分页查询场景
*
* url path: /v1/manage/scenes/page
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesPagePost(data: APIModels["V1ManageScenesPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageScenesPagePostResponse"]>({
    url: `/v1/manage/scenes/page`,
    data: data,
  });
}

/**
* 查询场景题目列表
*
* url path: /v1/manage/scene-questions/list
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsListPost(data: APIModels["V1ManageSceneQuestionsListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneQuestionsListPostResponse"]>({
    url: `/v1/manage/scene-questions/list`,
    data: data,
  });
}

/**
* 提交场景问题答案
*
* url path: /v1/manage/scene-exam/submit
* @tags 虚拟班组长-场景问题作答
*/
export function V1ManageSceneExamSubmitPost(data: APIModels["V1ManageSceneExamSubmitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneExamSubmitPostResponse"]>({
    url: `/v1/manage/scene-exam/submit`,
    data: data,
  });
}

/**
* 分页查询问题作答记录
*
* url path: /v1/manage/scene-exam/page
* @tags 虚拟班组长-场景问题作答
*/
export function V1ManageSceneExamPagePost(data: APIModels["V1ManageSceneExamPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneExamPagePostResponse"]>({
    url: `/v1/manage/scene-exam/page`,
    data: data,
  });
}

/**
* 查询场景线索列表
*
* url path: /v1/manage/scene-clues/list
* @tags 虚拟班组长-场景线索配置
*/
export function V1ManageSceneCluesListPost(data: APIModels["V1ManageSceneCluesListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageSceneCluesListPostResponse"]>({
    url: `/v1/manage/scene-clues/list`,
    data: data,
  });
}

/**
* 启用/停用
*
* url path: /v1/manage/question-user/status
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageQuestionUserStatusPost(data: APIModels["V1ManageQuestionUserStatusPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionUserStatusPostResponse"]>({
    url: `/v1/manage/question-user/status`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/question-user/page
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageQuestionUserPagePost(data: APIModels["V1ManageQuestionUserPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionUserPagePostResponse"]>({
    url: `/v1/manage/question-user/page`,
    data: data,
  });
}

/**
* 更新题目
*
* url path: /v1/manage/question-bank/updateQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankUpdateQuestionBankPost(data: APIModels["V1ManageQuestionBankUpdateQuestionBankPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankUpdateQuestionBankPostResponse"]>({
    url: `/v1/manage/question-bank/updateQuestionBank`,
    data: data,
  });
}

/**
* 接收生成试题错误信息
*
* url path: /v1/manage/question-bank/receiveAiGenerateError
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankReceiveAiGenerateErrorPost(data: APIModels["V1ManageQuestionBankReceiveAiGenerateErrorPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankReceiveAiGenerateErrorPostResponse"]>({
    url: `/v1/manage/question-bank/receiveAiGenerateError`,
    data: data,
  });
}

/**
* 新增题目
*
* url path: /v1/manage/question-bank/insertQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankInsertQuestionBankPost(data: APIModels["V1ManageQuestionBankInsertQuestionBankPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankInsertQuestionBankPostResponse"]>({
    url: `/v1/manage/question-bank/insertQuestionBank`,
    data: data,
  });
}

/**
* 导入Word格式文件题目
*
* url path: /v1/manage/question-bank/importQuestionByWord
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankImportQuestionByWordPost(data: APIModels["V1ManageQuestionBankImportQuestionByWordPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankImportQuestionByWordPostResponse"]>({
    url: `/v1/manage/question-bank/importQuestionByWord`,
    data: data,
  });
}

/**
* 导入Excel格式文件题目
*
* url path: /v1/manage/question-bank/importQuestionByExcel
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankImportQuestionByExcelPost(data: APIModels["V1ManageQuestionBankImportQuestionByExcelPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankImportQuestionByExcelPostResponse"]>({
    url: `/v1/manage/question-bank/importQuestionByExcel`,
    data: data,
  });
}

/**
* 导入数据，试题入库
*
* url path: /v1/manage/question-bank/importQuestionBatchInsert
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankImportQuestionBatchInsertPost(data: APIModels["V1ManageQuestionBankImportQuestionBatchInsertPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankImportQuestionBatchInsertPostResponse"]>({
    url: `/v1/manage/question-bank/importQuestionBatchInsert`,
    data: data,
  });
}

/**
* 分页查询题目
*
* url path: /v1/manage/question-bank/getQuestionBankPage
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankGetQuestionBankPagePost(data: APIModels["V1ManageQuestionBankGetQuestionBankPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankGetQuestionBankPagePostResponse"]>({
    url: `/v1/manage/question-bank/getQuestionBankPage`,
    data: data,
  });
}

/**
* 根据要求生成题目
*
* url path: /v1/manage/question-bank/generateQuestion
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankGenerateQuestionPost(data: APIModels["V1ManageQuestionBankGenerateQuestionPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankGenerateQuestionPostResponse"]>({
    url: `/v1/manage/question-bank/generateQuestion`,
    data: data,
  });
}

/**
* 上传文件根据要求生成题目
*
* url path: /v1/manage/question-bank/generateQuestionByFile
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankGenerateQuestionByFilePost(data: APIModels["V1ManageQuestionBankGenerateQuestionByFilePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankGenerateQuestionByFilePostResponse"]>({
    url: `/v1/manage/question-bank/generateQuestionByFile`,
    data: data,
  });
}

/**
* 批量更新题目
*
* url path: /v1/manage/question-bank/batchUpdateQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankBatchUpdateQuestionBankPost(data: APIModels["V1ManageQuestionBankBatchUpdateQuestionBankPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankBatchUpdateQuestionBankPostResponse"]>({
    url: `/v1/manage/question-bank/batchUpdateQuestionBank`,
    data: data,
  });
}

/**
* 批量保存Ai根据文件生成的题目
*
* url path: /v1/manage/question-bank/batchSaveQuestionBankFromAiGenerateByFile
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankBatchSaveQuestionBankFromAiGenerateByFilePost(data: APIModels["V1ManageQuestionBankBatchSaveQuestionBankFromAiGenerateByFilePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankBatchSaveQuestionBankFromAiGenerateByFilePostResponse"]>({
    url: `/v1/manage/question-bank/batchSaveQuestionBankFromAiGenerateByFile`,
    data: data,
  });
}

/**
* 批量新增题目
*
* url path: /v1/manage/question-bank/batchInsertQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankBatchInsertQuestionBankPost(data: APIModels["V1ManageQuestionBankBatchInsertQuestionBankPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankBatchInsertQuestionBankPostResponse"]>({
    url: `/v1/manage/question-bank/batchInsertQuestionBank`,
    data: data,
  });
}

/**
* 批量更新题目状态为确认
*
* url path: /v1/manage/question-bank/batchCompleteQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankBatchCompleteQuestionBankPost(data: APIModels["V1ManageQuestionBankBatchCompleteQuestionBankPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankBatchCompleteQuestionBankPostResponse"]>({
    url: `/v1/manage/question-bank/batchCompleteQuestionBank`,
    data: data,
  });
}

/**
* 查询列表
*
* url path: /v1/manage/question-bank-upload/getQuestionBankUploadList
* @tags 管理端-上传管理
*/
export function V1ManageQuestionBankUploadGetQuestionBankUploadListPost(data: APIModels["V1ManageQuestionBankUploadGetQuestionBankUploadListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankUploadGetQuestionBankUploadListPostResponse"]>({
    url: `/v1/manage/question-bank-upload/getQuestionBankUploadList`,
    data: data,
  });
}

/**
* 新增/更新题库类型
*
* url path: /v1/manage/question-bank-type/saveOrUpdateQuestionBankType
* @tags 管理端-题库类型管理管理
*/
export function V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePost(data: APIModels["V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankTypeSaveOrUpdateQuestionBankTypePostResponse"]>({
    url: `/v1/manage/question-bank-type/saveOrUpdateQuestionBankType`,
    data: data,
  });
}

/**
* 查询题库类型
*
* url path: /v1/manage/question-bank-type/getQuestionBankTypeList
* @tags 管理端-题库类型管理管理
*/
export function V1ManageQuestionBankTypeGetQuestionBankTypeListPost(data: APIModels["V1ManageQuestionBankTypeGetQuestionBankTypeListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankTypeGetQuestionBankTypeListPostResponse"]>({
    url: `/v1/manage/question-bank-type/getQuestionBankTypeList`,
    data: data,
  });
}

/**
* 新增/更新题库字段
*
* url path: /v1/manage/question-bank-column/saveOrUpdateQuestionBankColumn
* @tags 管理端-题库字段配置管理
*/
export function V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPost(data: APIModels["V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankColumnSaveOrUpdateQuestionBankColumnPostResponse"]>({
    url: `/v1/manage/question-bank-column/saveOrUpdateQuestionBankColumn`,
    data: data,
  });
}

/**
* 查询题库字段
*
* url path: /v1/manage/question-bank-column/getQuestionBankColumnList
* @tags 管理端-题库字段配置管理
*/
export function V1ManageQuestionBankColumnGetQuestionBankColumnListPost(data: APIModels["V1ManageQuestionBankColumnGetQuestionBankColumnListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankColumnGetQuestionBankColumnListPostResponse"]>({
    url: `/v1/manage/question-bank-column/getQuestionBankColumnList`,
    data: data,
  });
}

/**
* 获取题库字段数据映射列表
*
* url path: /v1/manage/question-bank-column-mapping/list
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingListPost(data: APIModels["V1ManageQuestionBankColumnMappingListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankColumnMappingListPostResponse"]>({
    url: `/v1/manage/question-bank-column-mapping/list`,
    data: data,
  });
}

/**
* 导入题库字段数据映射
*
* url path: /v1/manage/question-bank-column-mapping/import/{columnId}
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingImportColumnIdPost(query: APIModels["V1ManageQuestionBankColumnMappingImportColumnIdPostQuery"], params: APIModels["V1ManageQuestionBankColumnMappingImportColumnIdPostParam"]) {
  return httpAdaptor.post<APIModels["V1ManageQuestionBankColumnMappingImportColumnIdPostResponse"]>({
    url: `/v1/manage/question-bank-column-mapping/import/${params.columnId}`,
    query: query,
    param: params,
  });
}

/**
* 批量保存数据
*
* url path: /v1/manage/prompts/batch
* @tags 管理端-提示词管理
*/
export function V1ManagePromptsBatchPost(data: APIModels["V1ManagePromptsBatchPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManagePromptsBatchPostResponse"]>({
    url: `/v1/manage/prompts/batch`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/process-algorithm/page
* @tags 管理端-工艺算法管理
*/
export function V1ManageProcessAlgorithmPagePost(data: APIModels["V1ManageProcessAlgorithmPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageProcessAlgorithmPagePostResponse"]>({
    url: `/v1/manage/process-algorithm/page`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/position/page
* @tags 管理端-岗位管理
*/
export function V1ManagePositionPagePost(data: APIModels["V1ManagePositionPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManagePositionPagePostResponse"]>({
    url: `/v1/manage/position/page`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/manage/position/import
* @tags 管理端-岗位管理
*/
export function V1ManagePositionImportPost(data: APIModels["V1ManagePositionImportPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManagePositionImportPostResponse"]>({
    url: `/v1/manage/position/import`,
    data: data,
  });
}

/**
* 分页查询学习项目
*
* url path: /v1/manage/learning-projects/page
* @tags 管理端-学习项目配置
*/
export function V1ManageLearningProjectsPagePost(data: APIModels["V1ManageLearningProjectsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLearningProjectsPagePostResponse"]>({
    url: `/v1/manage/learning-projects/page`,
    data: data,
  });
}

/**
* 分页查询学习材料
*
* url path: /v1/manage/learning-materials/page
* @tags 管理端-学习材料配置
*/
export function V1ManageLearningMaterialsPagePost(data: APIModels["V1ManageLearningMaterialsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLearningMaterialsPagePostResponse"]>({
    url: `/v1/manage/learning-materials/page`,
    data: data,
  });
}

/**
* 获取项目集合查询接口
*
* url path: /v1/manage/large-screen/project
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenProjectPost(data: APIModels["V1ManageLargeScreenProjectPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenProjectPostResponse"]>({
    url: `/v1/manage/large-screen/project`,
    data: data,
  });
}

/**
* 工位使用时间图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/unitUse
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartUnitUsePost(data: APIModels["V1ManageLargeScreenChartUnitUsePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartUnitUsePostResponse"]>({
    url: `/v1/manage/large-screen/chart/unitUse`,
    data: data,
  });
}

/**
* 训练人数趋势图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/trainUser
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartTrainUserPost(data: APIModels["V1ManageLargeScreenChartTrainUserPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartTrainUserPostResponse"]>({
    url: `/v1/manage/large-screen/chart/trainUser`,
    data: data,
  });
}

/**
* 训练场次柱状图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/trainNum
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartTrainNumPost(data: APIModels["V1ManageLargeScreenChartTrainNumPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartTrainNumPostResponse"]>({
    url: `/v1/manage/large-screen/chart/trainNum`,
    data: data,
  });
}

/**
* 合格率趋势图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/passRate
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartPassRatePost(data: APIModels["V1ManageLargeScreenChartPassRatePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartPassRatePostResponse"]>({
    url: `/v1/manage/large-screen/chart/passRate`,
    data: data,
  });
}

/**
* 作业次数趋势图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/jobNum
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartJobNumPost(data: APIModels["V1ManageLargeScreenChartJobNumPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartJobNumPostResponse"]>({
    url: `/v1/manage/large-screen/chart/jobNum`,
    data: data,
  });
}

/**
* 合格率趋势图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/actionPassRate
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartActionPassRatePost(data: APIModels["V1ManageLargeScreenChartActionPassRatePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartActionPassRatePostResponse"]>({
    url: `/v1/manage/large-screen/chart/actionPassRate`,
    data: data,
  });
}

/**
* 动作缺陷次数图-图表查询接口
*
* url path: /v1/manage/large-screen/chart/actionDefect
* @tags 大屏相关数据统计
*/
export function V1ManageLargeScreenChartActionDefectPost(data: APIModels["V1ManageLargeScreenChartActionDefectPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageLargeScreenChartActionDefectPostResponse"]>({
    url: `/v1/manage/large-screen/chart/actionDefect`,
    data: data,
  });
}

/**
* 分页查询岗位
*
* url path: /v1/manage/jobs/page
* @tags 管理端-岗位配置
*/
export function V1ManageJobsPagePost(data: APIModels["V1ManageJobsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageJobsPagePostResponse"]>({
    url: `/v1/manage/jobs/page`,
    data: data,
  });
}

/**
* 分页查询岗级
*
* url path: /v1/manage/job-levels/page
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsPagePost(data: APIModels["V1ManageJobLevelsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageJobLevelsPagePostResponse"]>({
    url: `/v1/manage/job-levels/page`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/exam-record/page
* @tags 管理端-考试试题
*/
export function V1ManageExamRecordPagePost(data: APIModels["V1ManageExamRecordPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamRecordPagePostResponse"]>({
    url: `/v1/manage/exam-record/page`,
    data: data,
  });
}

/**
* 查询题库对应考题
*
* url path: /v1/manage/exam-paper/search-question-single
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperSearchQuestionSinglePost(data: APIModels["V1ManageExamPaperSearchQuestionSinglePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperSearchQuestionSinglePostResponse"]>({
    url: `/v1/manage/exam-paper/search-question-single`,
    data: data,
  });
}

/**
* 查询题库对应考题
*
* url path: /v1/manage/exam-paper/search-question-multiple
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperSearchQuestionMultiplePost(data: APIModels["V1ManageExamPaperSearchQuestionMultiplePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperSearchQuestionMultiplePostResponse"]>({
    url: `/v1/manage/exam-paper/search-question-multiple`,
    data: data,
  });
}

/**
* 发布
*
* url path: /v1/manage/exam-paper/release
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperReleasePost(data: APIModels["V1ManageExamPaperReleasePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperReleasePostResponse"]>({
    url: `/v1/manage/exam-paper/release`,
    data: data,
  });
}

/**
* 快速发布
*
* url path: /v1/manage/exam-paper/quickly-release
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperQuicklyReleasePost(data: APIModels["V1ManageExamPaperQuicklyReleasePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperQuicklyReleasePostResponse"]>({
    url: `/v1/manage/exam-paper/quickly-release`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/manage/exam-paper/page
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperPagePost(data: APIModels["V1ManageExamPaperPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperPagePostResponse"]>({
    url: `/v1/manage/exam-paper/page`,
    data: data,
  });
}

/**
* 复制初始化
*
* url path: /v1/manage/exam-paper/copy-init
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperCopyInitPost(data: APIModels["V1ManageExamPaperCopyInitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperCopyInitPostResponse"]>({
    url: `/v1/manage/exam-paper/copy-init`,
    data: data,
  });
}

/**
* 确认
*
* url path: /v1/manage/exam-paper/confirm
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperConfirmPost(data: APIModels["V1ManageExamPaperConfirmPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageExamPaperConfirmPostResponse"]>({
    url: `/v1/manage/exam-paper/confirm`,
    data: data,
  });
}

/**
* 查询字典数据列表
*
* url path: /v1/manage/dict-data/list
* @tags 管理端-系统字典数据管理
*/
export function V1ManageDictDataListPost(data: APIModels["V1ManageDictDataListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageDictDataListPostResponse"]>({
    url: `/v1/manage/dict-data/list`,
    data: data,
  });
}

/**
* 获取车型类型接口
*
* url path: /v1/manage/common/vehicle
* @tags 公共服务控制器
*/
export function V1ManageCommonVehiclePost(data: APIModels["V1ManageCommonVehiclePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageCommonVehiclePostResponse"]>({
    url: `/v1/manage/common/vehicle`,
    data: data,
  });
}

/**
* 用户集合
*
* url path: /v1/manage/common/user-list
* @tags 公共服务控制器
*/
export function V1ManageCommonUserListPost(data: APIModels["V1ManageCommonUserListPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageCommonUserListPostResponse"]>({
    url: `/v1/manage/common/user-list`,
    data: data,
  });
}

/**
* 获取获取岗位接口
*
* url path: /v1/manage/common/position
* @tags 公共服务控制器
*/
export function V1ManageCommonPositionPost(data: APIModels["V1ManageCommonPositionPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1ManageCommonPositionPostResponse"]>({
    url: `/v1/manage/common/position`,
    data: data,
  });
}

/**
* 停止视频流接口
*
* url path: /v1/location/study/video/stop
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyVideoStopPost(data: APIModels["V1LocationStudyVideoStopPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyVideoStopPostResponse"]>({
    url: `/v1/location/study/video/stop`,
    data: data,
  });
}

/**
* 创建视频流接口
*
* url path: /v1/location/study/video/create
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyVideoCreatePost(data: APIModels["V1LocationStudyVideoCreatePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyVideoCreatePostResponse"]>({
    url: `/v1/location/study/video/create`,
    data: data,
  });
}

/**
* 暂停训练接口
*
* url path: /v1/location/study/stop
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyStopPost(data: APIModels["V1LocationStudyStopPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyStopPostResponse"]>({
    url: `/v1/location/study/stop`,
    data: data,
  });
}

/**
* 开始训练接口
*
* url path: /v1/location/study/start
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyStartPost(data: APIModels["V1LocationStudyStartPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyStartPostResponse"]>({
    url: `/v1/location/study/start`,
    data: data,
  });
}

/**
* 完成训练接口
*
* url path: /v1/location/study/over
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyOverPost(data: APIModels["V1LocationStudyOverPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyOverPostResponse"]>({
    url: `/v1/location/study/over`,
    data: data,
  });
}

/**
* 操作日志-分页查询接口
*
* url path: /v1/location/study/operationLog/page
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyOperationLogPagePost(data: APIModels["V1LocationStudyOperationLogPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyOperationLogPagePostResponse"]>({
    url: `/v1/location/study/operationLog/page`,
    data: data,
  });
}

/**
* 初始化签到接口
*
* url path: /v1/location/study/init
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyInitPost(data: APIModels["V1LocationStudyInitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyInitPostResponse"]>({
    url: `/v1/location/study/init`,
    data: data,
  });
}

/**
* 退出训练接口
*
* url path: /v1/location/study/exit
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyExitPost(data: APIModels["V1LocationStudyExitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationStudyExitPostResponse"]>({
    url: `/v1/location/study/exit`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/location/home/<USER>/confirm-login
* @tags 工位端-home页控制器
*/
export function V1LocationHomeScanConfirmLoginPost(query: APIModels["V1LocationHomeScanConfirmLoginPostQuery"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeScanConfirmLoginPostResponse"]>({
    url: `/v1/location/home/<USER>/confirm-login`,
    query: query,
  });
}

/**
* 扫码查看项目订阅接口
*
* url path: /v1/location/home/<USER>/subscribe
* @tags 工位端-home页控制器
*/
export function V1LocationHomeProjectSubscribePost(data: APIModels["V1LocationHomeProjectSubscribePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeProjectSubscribePostResponse"]>({
    url: `/v1/location/home/<USER>/subscribe`,
    data: data,
  });
}

/**
* 项目订阅提交接口
*
* url path: /v1/location/home/<USER>/subscribe/submit
* @tags 工位端-home页控制器
*/
export function V1LocationHomeProjectSubscribeSubmitPost(data: APIModels["V1LocationHomeProjectSubscribeSubmitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeProjectSubscribeSubmitPostResponse"]>({
    url: `/v1/location/home/<USER>/subscribe/submit`,
    data: data,
  });
}

/**
* 取消项目订阅接口
*
* url path: /v1/location/home/<USER>/subscribe/cancel
* @tags 工位端-home页控制器
*/
export function V1LocationHomeProjectSubscribeCancelPost(data: APIModels["V1LocationHomeProjectSubscribeCancelPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeProjectSubscribeCancelPostResponse"]>({
    url: `/v1/location/home/<USER>/subscribe/cancel`,
    data: data,
  });
}

/**
* 分页查询不合格记录
*
* url path: /v1/location/home/<USER>/page
* @tags 工位端-home页控制器
*/
export function V1LocationHomeAlarmDetailsPagePost(data: APIModels["V1LocationHomeAlarmDetailsPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeAlarmDetailsPagePostResponse"]>({
    url: `/v1/location/home/<USER>/page`,
    data: data,
  });
}

/**
* 不合格记录统计查询
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeAlarmDetailsStatisticsPost(data: APIModels["V1LocationHomeAlarmDetailsStatisticsPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1LocationHomeAlarmDetailsStatisticsPostResponse"]>({
    url: `/v1/location/home/<USER>
    data: data,
  });
}

/**
* 用户工单数据查询接口
*
* url path: /v1/evaluate/task/userOrder/{userId}
* @tags 评定任务
*/
export function V1EvaluateTaskUserOrderUserIdPost(query: APIModels["V1EvaluateTaskUserOrderUserIdPostQuery"], params: APIModels["V1EvaluateTaskUserOrderUserIdPostParam"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskUserOrderUserIdPostResponse"]>({
    url: `/v1/evaluate/task/userOrder/${params.userId}`,
    query: query,
    param: params,
  });
}

/**
* 用户工单数据查询接口
*
* url path: /v1/evaluate/task/userOrder/page
* @tags 评定任务
*/
export function V1EvaluateTaskUserOrderPagePost(data: APIModels["V1EvaluateTaskUserOrderPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskUserOrderPagePostResponse"]>({
    url: `/v1/evaluate/task/userOrder/page`,
    data: data,
  });
}

/**
* 提交评定接口
*
* url path: /v1/evaluate/task/submit
* @tags 评定任务
*/
export function V1EvaluateTaskSubmitPost(data: APIModels["V1EvaluateTaskSubmitPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskSubmitPostResponse"]>({
    url: `/v1/evaluate/task/submit`,
    data: data,
  });
}

/**
* 评定任务保存接口
*
* url path: /v1/evaluate/task/save
* @tags 评定任务
*/
export function V1EvaluateTaskSavePost(data: APIModels["V1EvaluateTaskSavePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskSavePostResponse"]>({
    url: `/v1/evaluate/task/save`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/evaluate/task/page
* @tags 评定任务
*/
export function V1EvaluateTaskPagePost(data: APIModels["V1EvaluateTaskPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskPagePostResponse"]>({
    url: `/v1/evaluate/task/page`,
    data: data,
  });
}

/**
* 工单详情接口
*
* url path: /v1/evaluate/task/orderInfo/{id}
* @tags 评定任务
*/
export function V1EvaluateTaskOrderInfoIdPost(params: APIModels["V1EvaluateTaskOrderInfoIdPostParam"]) {
  return httpAdaptor.post<APIModels["V1EvaluateTaskOrderInfoIdPostResponse"]>({
    url: `/v1/evaluate/task/orderInfo/${params.id}`,
    param: params,
  });
}

/**
* 启用/停用接口
*
* url path: /v1/evaluate/config/status
* @tags 评定项目配置
*/
export function V1EvaluateConfigStatusPost(data: APIModels["V1EvaluateConfigStatusPostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateConfigStatusPostResponse"]>({
    url: `/v1/evaluate/config/status`,
    data: data,
  });
}

/**
* 评定项目保存接口
*
* url path: /v1/evaluate/config/save
* @tags 评定项目配置
*/
export function V1EvaluateConfigSavePost(data: APIModels["V1EvaluateConfigSavePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateConfigSavePostResponse"]>({
    url: `/v1/evaluate/config/save`,
    data: data,
  });
}

/**
* 分页查询数据
*
* url path: /v1/evaluate/config/page
* @tags 评定项目配置
*/
export function V1EvaluateConfigPagePost(data: APIModels["V1EvaluateConfigPagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1EvaluateConfigPagePostResponse"]>({
    url: `/v1/evaluate/config/page`,
    data: data,
  });
}

/**
* 消息推送接口
*
* url path: /v1/custom/sendMessage
* @tags custom-controller
*/
export function V1CustomSendMessagePost(data: APIModels["V1CustomSendMessagePostRequestBody"]) {
  return httpAdaptor.post<APIModels["V1CustomSendMessagePostResponse"]>({
    url: `/v1/custom/sendMessage`,
    data: data,
  });
}

/**
* 文件上传同步
*
* url path: /v1/common/file-upload/{businessType}
* @tags 文件上传服务
*/
export function V1CommonFileUploadBusinessTypePost(data: APIModels["V1CommonFileUploadBusinessTypePostRequestBody"], params: APIModels["V1CommonFileUploadBusinessTypePostParam"]) {
  return httpAdaptor.post<APIModels["V1CommonFileUploadBusinessTypePostResponse"]>({
    url: `/v1/common/file-upload/${params.businessType}`,
    data: data,
    param: params,
  });
}

/**
* 获取文件下载链接
*
* url path: /v1/common/file-upload/download/{fileId}
* @tags 文件上传服务
*/
export function V1CommonFileUploadDownloadFileIdPost(params: APIModels["V1CommonFileUploadDownloadFileIdPostParam"]) {
  return httpAdaptor.post<APIModels["V1CommonFileUploadDownloadFileIdPostResponse"]>({
    url: `/v1/common/file-upload/download/${params.fileId}`,
    param: params,
  });
}

/**
* 获取文件下载链接-path
*
* url path: /v1/common/file-upload/download-path/{fileId}
* @tags 文件上传服务
*/
export function V1CommonFileUploadDownloadPathFileIdPost(params: APIModels["V1CommonFileUploadDownloadPathFileIdPostParam"]) {
  return httpAdaptor.post<APIModels["V1CommonFileUploadDownloadPathFileIdPostResponse"]>({
    url: `/v1/common/file-upload/download-path/${params.fileId}`,
    param: params,
  });
}

/**
* 同步人员信息接口
*
* url path: /device/sync
* @tags 刷卡机
*/
export function DeviceSyncPost() {
  return httpAdaptor.post<APIModels["DeviceSyncPostResponse"]>({
    url: `/device/sync`,
  });
}

/**
* 注册人员信息接口
*
* url path: /device/register
* @tags 刷卡机
*/
export function DeviceRegisterPost(data: APIModels["DeviceRegisterPostRequestBody"]) {
  return httpAdaptor.post<APIModels["DeviceRegisterPostResponse"]>({
    url: `/device/register`,
    data: data,
  });
}

/**
* 删除人脸信息接口
*
* url path: /device/deleteFaceInfo/{employeeId}
* @tags 刷卡机
*/
export function DeviceDeleteFaceInfoEmployeeIdPost(params: APIModels["DeviceDeleteFaceInfoEmployeeIdPostParam"]) {
  return httpAdaptor.post<APIModels["DeviceDeleteFaceInfoEmployeeIdPostResponse"]>({
    url: `/device/deleteFaceInfo/${params.employeeId}`,
    param: params,
  });
}

/**
* 删除人员接口
*
* url path: /device/delete/{employeeId}
* @tags 刷卡机
*/
export function DeviceDeleteEmployeeIdPost(params: APIModels["DeviceDeleteEmployeeIdPostParam"]) {
  return httpAdaptor.post<APIModels["DeviceDeleteEmployeeIdPostResponse"]>({
    url: `/device/delete/${params.employeeId}`,
    param: params,
  });
}

/**
* 删除卡号信息接口
*
* url path: /device/delete/cardNo/{cardNo}
* @tags 刷卡机
*/
export function DeviceDeleteCardNoCardNoPost(params: APIModels["DeviceDeleteCardNoCardNoPostParam"]) {
  return httpAdaptor.post<APIModels["DeviceDeleteCardNoCardNoPostResponse"]>({
    url: `/device/delete/cardNo/${params.cardNo}`,
    param: params,
  });
}

/**
* 绑定人员与人脸接口
*
* url path: /device/bindingFace
* @tags 刷卡机
*/
export function DeviceBindingFacePost(data: APIModels["DeviceBindingFacePostRequestBody"]) {
  return httpAdaptor.post<APIModels["DeviceBindingFacePostResponse"]>({
    url: `/device/bindingFace`,
    data: data,
  });
}

/**
* 绑定人员与卡接口
*
* url path: /device/bindingCard
* @tags 刷卡机
*/
export function DeviceBindingCardPost(data: APIModels["DeviceBindingCardPostRequestBody"]) {
  return httpAdaptor.post<APIModels["DeviceBindingCardPostResponse"]>({
    url: `/device/bindingCard`,
    data: data,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/scan/qrcode
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginScanQrcode() {
  return httpAdaptor.get<APIModels["V1OpenApiUserLoginScanQrcodeGetResponse"]>({
    url: `/v1/open-api/user-login/scan/qrcode`,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/scan/login-status
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginScanLoginStatus(query: APIModels["V1OpenApiUserLoginScanLoginStatusGetQuery"]) {
  return httpAdaptor.get<APIModels["V1OpenApiUserLoginScanLoginStatusGetResponse"]>({
    url: `/v1/open-api/user-login/scan/login-status`,
    query: query,
  });
}

/**
* 
*
* url path: /v1/open-api/user-login/publicKey
* @tags 开放接口-用户登录
*/
export function V1OpenApiUserLoginPublicKey(query: APIModels["V1OpenApiUserLoginPublicKeyGetQuery"]) {
  return httpAdaptor.get<APIModels["V1OpenApiUserLoginPublicKeyGetResponse"]>({
    url: `/v1/open-api/user-login/publicKey`,
    query: query,
  });
}

/**
* 查询所有配置
*
* url path: /v1/open-api/sys-config/list
* @tags 开放接口-系统配置管理
*/
export function V1OpenApiSysConfigList() {
  return httpAdaptor.get<APIModels["V1OpenApiSysConfigListGetResponse"]>({
    url: `/v1/open-api/sys-config/list`,
  });
}

/**
* 
*
* url path: /v1/open-api/get/url/stream_{channel}
* @tags flv-controller
*/
export function V1OpenApiGetUrlStream_channel(params: APIModels["V1OpenApiGetUrlStream_channelGetParam"]) {
  return httpAdaptor.get<APIModels["V1OpenApiGetUrlStream_channelGetResponse"]>({
    url: `/v1/open-api/get/url/stream_${params.channel}`,
    param: params,
  });
}

/**
* 
*
* url path: /v1/open-api/get/flv/hls/stream_{channel}.flv
* @tags flv-controller
*/
export function V1OpenApiGetFlvHlsStream_channel_flv(params: APIModels["V1OpenApiGetFlvHlsStream_channel_flvGetParam"]) {
  return httpAdaptor.get<void>({
    url: `/v1/open-api/get/flv/hls/stream_${params.channel}.flv`,
    param: params,
  });
}

/**
* 
*
* url path: /v1/open-api/get/flv/hls/stream
* @tags flv-controller
*/
export function V1OpenApiGetFlvHlsStream(query: APIModels["V1OpenApiGetFlvHlsStreamGetQuery"]) {
  return httpAdaptor.get<void>({
    url: `/v1/open-api/get/flv/hls/stream`,
    query: query,
  });
}

/**
* 登录同步
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomeLoginSync() {
  return httpAdaptor.get<APIModels["V1MobileHomeLoginSyncGetResponse"]>({
    url: `/v1/mobile/home/<USER>
  });
}

/**
* 获取当前用户的试卷最新考试记录
*
* url path: /v1/mobile/home/<USER>/{paperId}
* @tags 移动端-home页控制器
*/
export function V1MobileHomeLastedExamRecordPaperId(params: APIModels["V1MobileHomeLastedExamRecordPaperIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1MobileHomeLastedExamRecordPaperIdGetResponse"]>({
    url: `/v1/mobile/home/<USER>/${params.paperId}`,
    param: params,
  });
}

/**
* 当前用户的考试统计查询
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomeExamStatistic() {
  return httpAdaptor.get<APIModels["V1MobileHomeExamStatisticGetResponse"]>({
    url: `/v1/mobile/home/<USER>
  });
}

/**
* 考试记录详情
*
* url path: /v1/mobile/home/<USER>/{id}
* @tags 移动端-home页控制器
*/
export function V1MobileHomeExamRecordId(params: APIModels["V1MobileHomeExamRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1MobileHomeExamRecordIdGetResponse"]>({
    url: `/v1/mobile/home/<USER>/${params.id}`,
    param: params,
  });
}

/**
* 试卷对应考题包括答案
*
* url path: /v1/mobile/home/<USER>/{id}
* @tags 移动端-home页控制器
*/
export function V1MobileHomeExamRecordPaperId(params: APIModels["V1MobileHomeExamRecordPaperIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1MobileHomeExamRecordPaperIdGetResponse"]>({
    url: `/v1/mobile/home/<USER>/${params.id}`,
    param: params,
  });
}

/**
* 试卷对应考题-不包括答案
*
* url path: /v1/mobile/home/<USER>/{id}
* @tags 移动端-home页控制器
*/
export function V1MobileHomeExamRecordPaperInitId(params: APIModels["V1MobileHomeExamRecordPaperInitIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1MobileHomeExamRecordPaperInitIdGetResponse"]>({
    url: `/v1/mobile/home/<USER>/${params.id}`,
    param: params,
  });
}

/**
* 试卷的考试历史记录
*
* url path: /v1/mobile/home/<USER>/{paperId}/{userId}
* @tags 移动端-home页控制器
*/
export function V1MobileHomeExamRecordHistoryPaperIdUserId(params: APIModels["V1MobileHomeExamRecordHistoryPaperIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1MobileHomeExamRecordHistoryPaperIdUserIdGetResponse"]>({
    url: `/v1/mobile/home/<USER>/${params.paperId}/${params.userId}`,
    param: params,
  });
}

/**
* 当前登录学员查询接口
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomeCurrentUser() {
  return httpAdaptor.get<APIModels["V1MobileHomeCurrentUserGetResponse"]>({
    url: `/v1/mobile/home/<USER>
  });
}

/**
* 当前时间查询接口
*
* url path: /v1/mobile/home/<USER>
* @tags 移动端-home页控制器
*/
export function V1MobileHomeCurrentDate() {
  return httpAdaptor.get<APIModels["V1MobileHomeCurrentDateGetResponse"]>({
    url: `/v1/mobile/home/<USER>
  });
}

/**
* 根据ID用户学习记录
*
* url path: /v1/manage/user-learnings/{learningDetailId}
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsLearningDetailId(params: APIModels["V1ManageUserLearningsLearningDetailIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageUserLearningsLearningDetailIdGetResponse"]>({
    url: `/v1/manage/user-learnings/${params.learningDetailId}`,
    param: params,
  });
}

/**
* 分页查询用户学习记录
*
* url path: /v1/manage/user-learnings/statistic
* @tags 管理端-用户学习记录
*/
export function V1ManageUserLearningsStatistic() {
  return httpAdaptor.get<APIModels["V1ManageUserLearningsStatisticGetResponse"]>({
    url: `/v1/manage/user-learnings/statistic`,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/tran-project/{id}
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectId(params: APIModels["V1ManageTranProjectIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTranProjectIdGetResponse"]>({
    url: `/v1/manage/tran-project/${params.id}`,
    param: params,
  });
}

/**
* 根据项目名称和类型查询接口
*
* url path: /v1/manage/tran-project/with-name
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectWithName(query: APIModels["V1ManageTranProjectWithNameGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageTranProjectWithNameGetResponse"]>({
    url: `/v1/manage/tran-project/with-name`,
    query: query,
  });
}

/**
* 工位管理绑定项目信息
*
* url path: /v1/manage/tran-project/bind-project/{locationId}
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectBindProjectLocationId(query: APIModels["V1ManageTranProjectBindProjectLocationIdGetQuery"], params: APIModels["V1ManageTranProjectBindProjectLocationIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTranProjectBindProjectLocationIdGetResponse"]>({
    url: `/v1/manage/tran-project/bind-project/${params.locationId}`,
    query: query,
    param: params,
  });
}

/**
* 根据ID查询记录汇总
*
* url path: /v1/manage/train-study-records/{id}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsId(params: APIModels["V1ManageTrainStudyRecordsIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsIdGetResponse"]>({
    url: `/v1/manage/train-study-records/${params.id}`,
    param: params,
  });
}

/**
* 汇总记录中已存在的用户列表
*
* url path: /v1/manage/train-study-records/users
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsUsers() {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsUsersGetResponse"]>({
    url: `/v1/manage/train-study-records/users`,
  });
}

/**
* 获取不合格记录图片url
*
* url path: /v1/manage/train-study-records/record-detail-alarm/url/{recordDetailAlarmId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId(params: APIModels["V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmIdGetResponse"]>({
    url: `/v1/manage/train-study-records/record-detail-alarm/url/${params.recordDetailAlarmId}`,
    param: params,
  });
}

/**
* 获取不合格记录图片的bytes结果
*
* url path: /v1/manage/train-study-records/record-detail-alarm/base64/{recordDetailAlarmId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmId(params: APIModels["V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsRecordDetailAlarmBase64RecordDetailAlarmIdGetResponse"]>({
    url: `/v1/manage/train-study-records/record-detail-alarm/base64/${params.recordDetailAlarmId}`,
    param: params,
  });
}

/**
* 排名统计
*
* url path: /v1/manage/train-study-records/rank/{recordId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsRankRecordId(params: APIModels["V1ManageTrainStudyRecordsRankRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsRankRecordIdGetResponse"]>({
    url: `/v1/manage/train-study-records/rank/${params.recordId}`,
    param: params,
  });
}

/**
* 所有排名
*
* url path: /v1/manage/train-study-records/rank-all/{projectId}/{type}/{start}/{end}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsRankAllProjectIdTypeStartEnd(params: APIModels["V1ManageTrainStudyRecordsRankAllProjectIdTypeStartEndGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsRankAllProjectIdTypeStartEndGetResponse"]>({
    url: `/v1/manage/train-study-records/rank-all/${params.projectId}/${params.type}/${params.start}/${params.end}`,
    param: params,
  });
}

/**
* 通过考试项目ID查询比赛排名
*
* url path: /v1/manage/train-study-records/race-rank/{projectId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsRaceRankProjectId(params: APIModels["V1ManageTrainStudyRecordsRaceRankProjectIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsRaceRankProjectIdGetResponse"]>({
    url: `/v1/manage/train-study-records/race-rank/${params.projectId}`,
    param: params,
  });
}

/**
* 汇总记录中已存在的项目列表
*
* url path: /v1/manage/train-study-records/projects
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsProjects(query: APIModels["V1ManageTrainStudyRecordsProjectsGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsProjectsGetResponse"]>({
    url: `/v1/manage/train-study-records/projects`,
    query: query,
  });
}

/**
* 训练进度统计查询接口
*
* url path: /v1/manage/train-study-records/progress-statistics/{recordId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsProgressStatisticsRecordId(params: APIModels["V1ManageTrainStudyRecordsProgressStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsProgressStatisticsRecordIdGetResponse"]>({
    url: `/v1/manage/train-study-records/progress-statistics/${params.recordId}`,
    param: params,
  });
}

/**
* 月度训练数据统计
*
* url path: /v1/manage/train-study-records/monthly-statistics
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsMonthlyStatistics() {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsMonthlyStatisticsGetResponse"]>({
    url: `/v1/manage/train-study-records/monthly-statistics`,
  });
}

/**
* 最新训练数据查询接口
*
* url path: /v1/manage/train-study-records/last-statistics/{recordId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsLastStatisticsRecordId(params: APIModels["V1ManageTrainStudyRecordsLastStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsLastStatisticsRecordIdGetResponse"]>({
    url: `/v1/manage/train-study-records/last-statistics/${params.recordId}`,
    param: params,
  });
}

/**
* 数据统计查询接口
*
* url path: /v1/manage/train-study-records/data-statistics/{recordId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsDataStatisticsRecordId(params: APIModels["V1ManageTrainStudyRecordsDataStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsDataStatisticsRecordIdGetResponse"]>({
    url: `/v1/manage/train-study-records/data-statistics/${params.recordId}`,
    param: params,
  });
}

/**
* 通过考试项目ID查询考核排名
*
* url path: /v1/manage/train-study-records/check-rank/{projectId}
* @tags 管理端-培训记录汇总管理
*/
export function V1ManageTrainStudyRecordsCheckRankProjectId(params: APIModels["V1ManageTrainStudyRecordsCheckRankProjectIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainStudyRecordsCheckRankProjectIdGetResponse"]>({
    url: `/v1/manage/train-study-records/check-rank/${params.projectId}`,
    param: params,
  });
}

/**
* 根据ID查询训练预约
*
* url path: /v1/manage/train-bookings/{bookingId}
* @tags 虚拟班组长-训练预约配置
*/
export function V1ManageTrainBookingsBookingId(params: APIModels["V1ManageTrainBookingsBookingIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageTrainBookingsBookingIdGetResponse"]>({
    url: `/v1/manage/train-bookings/${params.bookingId}`,
    param: params,
  });
}

/**
* 删除训练预约
*
* url path: /v1/manage/train-bookings/{bookingId}
* @tags 虚拟班组长-训练预约配置
*/
export function V1ManageTrainBookingsBookingIdDelete(params: APIModels["V1ManageTrainBookingsBookingIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageTrainBookingsBookingIdDeleteResponse"]>({
    url: `/v1/manage/train-bookings/${params.bookingId}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/sys-user/{id}
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserId(params: APIModels["V1ManageSysUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUserIdGetResponse"]>({
    url: `/v1/manage/sys-user/${params.id}`,
    param: params,
  });
}

/**
* 获取用户岗位、岗级的统计
*
* url path: /v1/manage/sys-user/job-statistics
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserJobStatistics(query: APIModels["V1ManageSysUserJobStatisticsGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUserJobStatisticsGetResponse"]>({
    url: `/v1/manage/sys-user/job-statistics`,
    query: query,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/sys-unit/{id}
* @tags 管理端-组织机构(工位)管理
*/
export function V1ManageSysUnitId(params: APIModels["V1ManageSysUnitIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUnitIdGetResponse"]>({
    url: `/v1/manage/sys-unit/${params.id}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/sys-unit-terminal/{id}
* @tags 管理端-工位终端管理
*/
export function V1ManageSysUnitTerminalId(params: APIModels["V1ManageSysUnitTerminalIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUnitTerminalIdGetResponse"]>({
    url: `/v1/manage/sys-unit-terminal/${params.id}`,
    param: params,
  });
}

/**
* 获取终端列表接口
*
* url path: /v1/manage/sys-unit-terminal/list
* @tags 管理端-工位终端管理
*/
export function V1ManageSysUnitTerminalList() {
  return httpAdaptor.get<APIModels["V1ManageSysUnitTerminalListGetResponse"]>({
    url: `/v1/manage/sys-unit-terminal/list`,
  });
}

/**
* 获取相机照片接口
*
* url path: /v1/manage/sys-unit-terminal/capture/{ip}
* @tags 管理端-工位终端管理
*/
export function V1ManageSysUnitTerminalCaptureIp(params: APIModels["V1ManageSysUnitTerminalCaptureIpGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUnitTerminalCaptureIpGetResponse"]>({
    url: `/v1/manage/sys-unit-terminal/capture/${params.ip}`,
    param: params,
  });
}

/**
* 单位对应终端详情接口
*
* url path: /v1/manage/sys-unit-terminal/bind-location/{locationId}
* @tags 管理端-工位终端管理
*/
export function V1ManageSysUnitTerminalBindLocationLocationId(params: APIModels["V1ManageSysUnitTerminalBindLocationLocationIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysUnitTerminalBindLocationLocationIdGetResponse"]>({
    url: `/v1/manage/sys-unit-terminal/bind-location/${params.locationId}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/sys-org/{id}
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgId(params: APIModels["V1ManageSysOrgIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysOrgIdGetResponse"]>({
    url: `/v1/manage/sys-org/${params.id}`,
    param: params,
  });
}

/**
* 用户组织列表接口
*
* url path: /v1/manage/sys-org/userList/{userId}
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgUserListUserId(params: APIModels["V1ManageSysOrgUserListUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSysOrgUserListUserIdGetResponse"]>({
    url: `/v1/manage/sys-org/userList/${params.userId}`,
    param: params,
  });
}

/**
* 树
*
* url path: /v1/manage/sys-org/tree
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgTree(query: APIModels["V1ManageSysOrgTreeGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageSysOrgTreeGetResponse"]>({
    url: `/v1/manage/sys-org/tree`,
    query: query,
  });
}

/**
* 查询所有配置
*
* url path: /v1/manage/sys-config/list
* @tags 管理端-系统配置管理
*/
export function V1ManageSysConfigList() {
  return httpAdaptor.get<APIModels["V1ManageSysConfigListGetResponse"]>({
    url: `/v1/manage/sys-config/list`,
  });
}

/**
* 根据ID查询场景
*
* url path: /v1/manage/scenes/{sceneId}
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesSceneId(params: APIModels["V1ManageScenesSceneIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageScenesSceneIdGetResponse"]>({
    url: `/v1/manage/scenes/${params.sceneId}`,
    param: params,
  });
}

/**
* 删除场景
*
* url path: /v1/manage/scenes/{sceneId}
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesSceneIdDelete(params: APIModels["V1ManageScenesSceneIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageScenesSceneIdDeleteResponse"]>({
    url: `/v1/manage/scenes/${params.sceneId}`,
    param: params,
  });
}

/**
* 获取当前用户已授权的场景列表
*
* url path: /v1/manage/scenes/current-user/list
* @tags 虚拟班组长-场景配置
*/
export function V1ManageScenesCurrentUserList(query: APIModels["V1ManageScenesCurrentUserListGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageScenesCurrentUserListGetResponse"]>({
    url: `/v1/manage/scenes/current-user/list`,
    query: query,
  });
}

/**
* 根据场景ID获取随机的题目
*
* url path: /v1/manage/scene-questions/{sceneId}/random
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsSceneIdRandom(query: APIModels["V1ManageSceneQuestionsSceneIdRandomGetQuery"], params: APIModels["V1ManageSceneQuestionsSceneIdRandomGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSceneQuestionsSceneIdRandomGetResponse"]>({
    url: `/v1/manage/scene-questions/${params.sceneId}/random`,
    query: query,
    param: params,
  });
}

/**
* 根据ID查询场景题目
*
* url path: /v1/manage/scene-questions/{questionId}
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsQuestionId(params: APIModels["V1ManageSceneQuestionsQuestionIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSceneQuestionsQuestionIdGetResponse"]>({
    url: `/v1/manage/scene-questions/${params.questionId}`,
    param: params,
  });
}

/**
* 删除场景题目
*
* url path: /v1/manage/scene-questions/{questionId}
* @tags 虚拟班组长-场景题目配置
*/
export function V1ManageSceneQuestionsQuestionIdDelete(params: APIModels["V1ManageSceneQuestionsQuestionIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageSceneQuestionsQuestionIdDeleteResponse"]>({
    url: `/v1/manage/scene-questions/${params.questionId}`,
    param: params,
  });
}

/**
* 根据ID查询场景线索
*
* url path: /v1/manage/scene-clues/{clueId}
* @tags 虚拟班组长-场景线索配置
*/
export function V1ManageSceneCluesClueId(params: APIModels["V1ManageSceneCluesClueIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageSceneCluesClueIdGetResponse"]>({
    url: `/v1/manage/scene-clues/${params.clueId}`,
    param: params,
  });
}

/**
* 删除场景线索
*
* url path: /v1/manage/scene-clues/{clueId}
* @tags 虚拟班组长-场景线索配置
*/
export function V1ManageSceneCluesClueIdDelete(params: APIModels["V1ManageSceneCluesClueIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageSceneCluesClueIdDeleteResponse"]>({
    url: `/v1/manage/scene-clues/${params.clueId}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/question-user/{id}
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageQuestionUserId(params: APIModels["V1ManageQuestionUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionUserIdGetResponse"]>({
    url: `/v1/manage/question-user/${params.id}`,
    param: params,
  });
}

/**
* 根据题目Id获取题目详情
*
* url path: /v1/manage/question-bank/getInfo
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankGetInfo(query: APIModels["V1ManageQuestionBankGetInfoGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankGetInfoGetResponse"]>({
    url: `/v1/manage/question-bank/getInfo`,
    query: query,
  });
}

/**
* 下载Word导入试题模版
*
* url path: /v1/manage/question-bank/downloadImportQuestionForWord
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankDownloadImportQuestionForWord() {
  return httpAdaptor.get<void>({
    url: `/v1/manage/question-bank/downloadImportQuestionForWord`,
  });
}

/**
* 导入Excel格式文件题目
*
* url path: /v1/manage/question-bank/downloadImportQuestionForExcel
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankDownloadImportQuestionForExcel() {
  return httpAdaptor.get<void>({
    url: `/v1/manage/question-bank/downloadImportQuestionForExcel`,
  });
}

/**
* 删除题目
*
* url path: /v1/manage/question-bank/deleteQuestionBank
* @tags 管理端-题库管理
*/
export function V1ManageQuestionBankDeleteQuestionBank(query: APIModels["V1ManageQuestionBankDeleteQuestionBankGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankDeleteQuestionBankGetResponse"]>({
    url: `/v1/manage/question-bank/deleteQuestionBank`,
    query: query,
  });
}

/**
* 根据Id获取详情
*
* url path: /v1/manage/question-bank-upload/getQuestionBankUploadDetailById
* @tags 管理端-上传管理
*/
export function V1ManageQuestionBankUploadGetQuestionBankUploadDetailById(query: APIModels["V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIdGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankUploadGetQuestionBankUploadDetailByIdGetResponse"]>({
    url: `/v1/manage/question-bank-upload/getQuestionBankUploadDetailById`,
    query: query,
  });
}

/**
* 根据id查询
*
* url path: /v1/manage/question-bank-type/getQuestionBankTypeById
* @tags 管理端-题库类型管理管理
*/
export function V1ManageQuestionBankTypeGetQuestionBankTypeById(query: APIModels["V1ManageQuestionBankTypeGetQuestionBankTypeByIdGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankTypeGetQuestionBankTypeByIdGetResponse"]>({
    url: `/v1/manage/question-bank-type/getQuestionBankTypeById`,
    query: query,
  });
}

/**
* 根据id删除
*
* url path: /v1/manage/question-bank-type/deleteById
* @tags 管理端-题库类型管理管理
*/
export function V1ManageQuestionBankTypeDeleteById(query: APIModels["V1ManageQuestionBankTypeDeleteByIdGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankTypeDeleteByIdGetResponse"]>({
    url: `/v1/manage/question-bank-type/deleteById`,
    query: query,
  });
}

/**
* 根据id查询
*
* url path: /v1/manage/question-bank-column/getQuestionBankColumnById
* @tags 管理端-题库字段配置管理
*/
export function V1ManageQuestionBankColumnGetQuestionBankColumnById(query: APIModels["V1ManageQuestionBankColumnGetQuestionBankColumnByIdGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankColumnGetQuestionBankColumnByIdGetResponse"]>({
    url: `/v1/manage/question-bank-column/getQuestionBankColumnById`,
    query: query,
  });
}

/**
* 根据id和key查询
*
* url path: /v1/manage/question-bank-column/getByColumnIdAndKey
* @tags 管理端-题库字段配置管理
*/
export function V1ManageQuestionBankColumnGetByColumnIdAndKey(query: APIModels["V1ManageQuestionBankColumnGetByColumnIdAndKeyGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankColumnGetByColumnIdAndKeyGetResponse"]>({
    url: `/v1/manage/question-bank-column/getByColumnIdAndKey`,
    query: query,
  });
}

/**
* 根据id删除
*
* url path: /v1/manage/question-bank-column/deleteById
* @tags 管理端-题库字段配置管理
*/
export function V1ManageQuestionBankColumnDeleteById(query: APIModels["V1ManageQuestionBankColumnDeleteByIdGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankColumnDeleteByIdGetResponse"]>({
    url: `/v1/manage/question-bank-column/deleteById`,
    query: query,
  });
}

/**
* 根据columnId和value查询第三方系统数据 columnId 和 columnKey 不能同时为空
*
* url path: /v1/manage/question-bank-column-mapping/getByColumnIdAndValue
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingGetByColumnIdAndValue(query: APIModels["V1ManageQuestionBankColumnMappingGetByColumnIdAndValueGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankColumnMappingGetByColumnIdAndValueGetResponse"]>({
    url: `/v1/manage/question-bank-column-mapping/getByColumnIdAndValue`,
    query: query,
  });
}

/**
* 根据columnId和value和propertyKey查询第三方系统数据  columnId 和 columnKey 不能同时为空
*
* url path: /v1/manage/question-bank-column-mapping/getByColumnIdAndValueAndPropertyKey
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingGetByColumnIdAndValueAndPropertyKey(query: APIModels["V1ManageQuestionBankColumnMappingGetByColumnIdAndValueAndPropertyKeyGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageQuestionBankColumnMappingGetByColumnIdAndValueAndPropertyKeyGetResponse"]>({
    url: `/v1/manage/question-bank-column-mapping/getByColumnIdAndValueAndPropertyKey`,
    query: query,
  });
}

/**
* 导出题库字段数据映射
*
* url path: /v1/manage/question-bank-column-mapping/export
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingExport(query: APIModels["V1ManageQuestionBankColumnMappingExportGetQuery"]) {
  return httpAdaptor.get<void>({
    url: `/v1/manage/question-bank-column-mapping/export`,
    query: query,
  });
}

/**
* 导出题库字段数据映射模板
*
* url path: /v1/manage/question-bank-column-mapping/exportTemplate
* @tags 管理端-题库字段数据映射管理
*/
export function V1ManageQuestionBankColumnMappingExportTemplate(query: APIModels["V1ManageQuestionBankColumnMappingExportTemplateGetQuery"]) {
  return httpAdaptor.get<void>({
    url: `/v1/manage/question-bank-column-mapping/exportTemplate`,
    query: query,
  });
}

/**
* 根据题库类型ID查询数据
*
* url path: /v1/manage/prompts/{bankTypeId}
* @tags 管理端-提示词管理
*/
export function V1ManagePromptsBankTypeId(params: APIModels["V1ManagePromptsBankTypeIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManagePromptsBankTypeIdGetResponse"]>({
    url: `/v1/manage/prompts/${params.bankTypeId}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/process-algorithm/with-code
* @tags 管理端-工艺算法管理
*/
export function V1ManageProcessAlgorithmWithCode(query: APIModels["V1ManageProcessAlgorithmWithCodeGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageProcessAlgorithmWithCodeGetResponse"]>({
    url: `/v1/manage/process-algorithm/with-code`,
    query: query,
  });
}

/**
* 根据ID查询学习项目
*
* url path: /v1/manage/learning-projects/{projectId}
* @tags 管理端-学习项目配置
*/
export function V1ManageLearningProjectsProjectId(params: APIModels["V1ManageLearningProjectsProjectIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageLearningProjectsProjectIdGetResponse"]>({
    url: `/v1/manage/learning-projects/${params.projectId}`,
    param: params,
  });
}

/**
* 删除学习项目
*
* url path: /v1/manage/learning-projects/{projectId}
* @tags 管理端-学习项目配置
*/
export function V1ManageLearningProjectsProjectIdDelete(params: APIModels["V1ManageLearningProjectsProjectIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageLearningProjectsProjectIdDeleteResponse"]>({
    url: `/v1/manage/learning-projects/${params.projectId}`,
    param: params,
  });
}

/**
* 根据ID查询学习材料
*
* url path: /v1/manage/learning-materials/{materialId}
* @tags 管理端-学习材料配置
*/
export function V1ManageLearningMaterialsMaterialId(params: APIModels["V1ManageLearningMaterialsMaterialIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageLearningMaterialsMaterialIdGetResponse"]>({
    url: `/v1/manage/learning-materials/${params.materialId}`,
    param: params,
  });
}

/**
* 删除学习材料
*
* url path: /v1/manage/learning-materials/{materialId}
* @tags 管理端-学习材料配置
*/
export function V1ManageLearningMaterialsMaterialIdDelete(params: APIModels["V1ManageLearningMaterialsMaterialIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageLearningMaterialsMaterialIdDeleteResponse"]>({
    url: `/v1/manage/learning-materials/${params.materialId}`,
    param: params,
  });
}

/**
* 根据ID查询岗位
*
* url path: /v1/manage/jobs/{jobId}
* @tags 管理端-岗位配置
*/
export function V1ManageJobsJobId(params: APIModels["V1ManageJobsJobIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageJobsJobIdGetResponse"]>({
    url: `/v1/manage/jobs/${params.jobId}`,
    param: params,
  });
}

/**
* 删除岗位
*
* url path: /v1/manage/jobs/{jobId}
* @tags 管理端-岗位配置
*/
export function V1ManageJobsJobIdDelete(params: APIModels["V1ManageJobsJobIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageJobsJobIdDeleteResponse"]>({
    url: `/v1/manage/jobs/${params.jobId}`,
    param: params,
  });
}

/**
* 根据ID查询岗级
*
* url path: /v1/manage/job-levels/{jobLevelId}
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsJobLevelId(params: APIModels["V1ManageJobLevelsJobLevelIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageJobLevelsJobLevelIdGetResponse"]>({
    url: `/v1/manage/job-levels/${params.jobLevelId}`,
    param: params,
  });
}

/**
* 删除岗级
*
* url path: /v1/manage/job-levels/{jobLevelId}
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsJobLevelIdDelete(params: APIModels["V1ManageJobLevelsJobLevelIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageJobLevelsJobLevelIdDeleteResponse"]>({
    url: `/v1/manage/job-levels/${params.jobLevelId}`,
    param: params,
  });
}

/**
* 根据岗位ID查询最高岗级
*
* url path: /v1/manage/job-levels/max-level/{jobId}
* @tags 管理端-岗级配置
*/
export function V1ManageJobLevelsMaxLevelJobId(params: APIModels["V1ManageJobLevelsMaxLevelJobIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageJobLevelsMaxLevelJobIdGetResponse"]>({
    url: `/v1/manage/job-levels/max-level/${params.jobId}`,
    param: params,
  });
}

/**
* 学员对应考题
*
* url path: /v1/manage/exam-record/{id}
* @tags 管理端-考试试题
*/
export function V1ManageExamRecordId(params: APIModels["V1ManageExamRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageExamRecordIdGetResponse"]>({
    url: `/v1/manage/exam-record/${params.id}`,
    param: params,
  });
}

/**
* 已完成考试的统计查询接口
*
* url path: /v1/manage/exam-record/completed-statistics/{paperId}
* @tags 管理端-考试试题
*/
export function V1ManageExamRecordCompletedStatisticsPaperId(params: APIModels["V1ManageExamRecordCompletedStatisticsPaperIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageExamRecordCompletedStatisticsPaperIdGetResponse"]>({
    url: `/v1/manage/exam-record/completed-statistics/${params.paperId}`,
    param: params,
  });
}

/**
* 详情接口
*
* url path: /v1/manage/exam-paper/{id}
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperId(params: APIModels["V1ManageExamPaperIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageExamPaperIdGetResponse"]>({
    url: `/v1/manage/exam-paper/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/exam-paper/{id}
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperIdDelete(params: APIModels["V1ManageExamPaperIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageExamPaperIdDeleteResponse"]>({
    url: `/v1/manage/exam-paper/${params.id}`,
    param: params,
  });
}

/**
* 试卷数据来源查询接口
*
* url path: /v1/manage/exam-paper/paper-source/{recordId}
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperPaperSourceRecordId(params: APIModels["V1ManageExamPaperPaperSourceRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageExamPaperPaperSourceRecordIdGetResponse"]>({
    url: `/v1/manage/exam-paper/paper-source/${params.recordId}`,
    param: params,
  });
}

/**
* 试题下载
*
* url path: /v1/manage/exam-paper/download/{id}
* @tags 管理端-试卷管理
*/
export function V1ManageExamPaperDownloadId(query: APIModels["V1ManageExamPaperDownloadIdGetQuery"], params: APIModels["V1ManageExamPaperDownloadIdGetParam"]) {
  return httpAdaptor.get<void>({
    url: `/v1/manage/exam-paper/download/${params.id}`,
    query: query,
    param: params,
  });
}

/**
* 查询题库对应考题
*
* url path: /v1/manage/exam-paper-question/list/{paperId}
* @tags 管理端-试卷试题管理
*/
export function V1ManageExamPaperQuestionListPaperId(params: APIModels["V1ManageExamPaperQuestionListPaperIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageExamPaperQuestionListPaperIdGetResponse"]>({
    url: `/v1/manage/exam-paper-question/list/${params.paperId}`,
    param: params,
  });
}

/**
* 根据ID查询字典数据
*
* url path: /v1/manage/dict-data/{dictDataId}
* @tags 管理端-系统字典数据管理
*/
export function V1ManageDictDataDictDataId(params: APIModels["V1ManageDictDataDictDataIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1ManageDictDataDictDataIdGetResponse"]>({
    url: `/v1/manage/dict-data/${params.dictDataId}`,
    param: params,
  });
}

/**
* 删除字典数据
*
* url path: /v1/manage/dict-data/{dictDataId}
* @tags 管理端-系统字典数据管理
*/
export function V1ManageDictDataDictDataIdDelete(params: APIModels["V1ManageDictDataDictDataIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageDictDataDictDataIdDeleteResponse"]>({
    url: `/v1/manage/dict-data/${params.dictDataId}`,
    param: params,
  });
}

/**
* 获取用户ID、名称的查询接口
*
* url path: /v1/manage/common/user-id-name
* @tags 公共服务控制器
*/
export function V1ManageCommonUserIdName() {
  return httpAdaptor.get<APIModels["V1ManageCommonUserIdNameGetResponse"]>({
    url: `/v1/manage/common/user-id-name`,
  });
}

/**
* 获取培训状态枚举
*
* url path: /v1/manage/common/train-status-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonTrainStatusEnum(query: APIModels["V1ManageCommonTrainStatusEnumGetQuery"]) {
  return httpAdaptor.get<APIModels["V1ManageCommonTrainStatusEnumGetResponse"]>({
    url: `/v1/manage/common/train-status-enum`,
    query: query,
  });
}

/**
* 获取题型类型查询接口
*
* url path: /v1/manage/common/question-type-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonQuestionTypeEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonQuestionTypeEnumGetResponse"]>({
    url: `/v1/manage/common/question-type-enum`,
  });
}

/**
* 项目类型枚举
*
* url path: /v1/manage/common/project-type-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonProjectTypeEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonProjectTypeEnumGetResponse"]>({
    url: `/v1/manage/common/project-type-enum`,
  });
}

/**
* 获取试卷类型查询接口
*
* url path: /v1/manage/common/paper-type-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonPaperTypeEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonPaperTypeEnumGetResponse"]>({
    url: `/v1/manage/common/paper-type-enum`,
  });
}

/**
* 判断图形枚举
*
* url path: /v1/manage/common/judge-type-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonJudgeTypeEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonJudgeTypeEnumGetResponse"]>({
    url: `/v1/manage/common/judge-type-enum`,
  });
}

/**
* 获取计次算法查询接口
*
* url path: /v1/manage/common/count-algorithm-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonCountAlgorithmEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonCountAlgorithmEnumGetResponse"]>({
    url: `/v1/manage/common/count-algorithm-enum`,
  });
}

/**
* 模型分类查询接口
*
* url path: /v1/manage/common/action-type-enum
* @tags 公共服务控制器
*/
export function V1ManageCommonActionTypeEnum() {
  return httpAdaptor.get<APIModels["V1ManageCommonActionTypeEnumGetResponse"]>({
    url: `/v1/manage/common/action-type-enum`,
  });
}

/**
* 获取汇总数据
*
* url path: /v1/location/study/train-record/{projectId}/{userId}
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyTrainRecordProjectIdUserId(params: APIModels["V1LocationStudyTrainRecordProjectIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationStudyTrainRecordProjectIdUserIdGetResponse"]>({
    url: `/v1/location/study/train-record/${params.projectId}/${params.userId}`,
    param: params,
  });
}

/**
* 训练项目查询接口
*
* url path: /v1/location/study/train-project/{locationId}/{userId}
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyTrainProjectLocationIdUserId(params: APIModels["V1LocationStudyTrainProjectLocationIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationStudyTrainProjectLocationIdUserIdGetResponse"]>({
    url: `/v1/location/study/train-project/${params.locationId}/${params.userId}`,
    param: params,
  });
}

/**
* 获取比赛报告
*
* url path: /v1/location/study/race/{projectId}/{userId}
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyRaceProjectIdUserId(params: APIModels["V1LocationStudyRaceProjectIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationStudyRaceProjectIdUserIdGetResponse"]>({
    url: `/v1/location/study/race/${params.projectId}/${params.userId}`,
    param: params,
  });
}

/**
* 获取本次培训时长
*
* url path: /v1/location/study/detail-duration/{detailId}
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyDetailDurationDetailId(params: APIModels["V1LocationStudyDetailDurationDetailIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationStudyDetailDurationDetailIdGetResponse"]>({
    url: `/v1/location/study/detail-duration/${params.detailId}`,
    param: params,
  });
}

/**
* 获取考核报告
*
* url path: /v1/location/study/check/{projectId}/{userId}
* @tags 工位端-学习页控制器
*/
export function V1LocationStudyCheckProjectIdUserId(params: APIModels["V1LocationStudyCheckProjectIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationStudyCheckProjectIdUserIdGetResponse"]>({
    url: `/v1/location/study/check/${params.projectId}/${params.userId}`,
    param: params,
  });
}

/**
* 用户首页数据（小程序）查询接口
*
* url path: /v1/location/home/<USER>/{userId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeUserDataUserId(params: APIModels["V1LocationHomeUserDataUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeUserDataUserIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.userId}`,
    param: params,
  });
}

/**
* 获取不合格记录图片的bytes结果
*
* url path: /v1/location/home/<USER>/base64/{recordDetailAlarmId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmId(params: APIModels["V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeRecordDetailAlarmBase64RecordDetailAlarmIdGetResponse"]>({
    url: `/v1/location/home/<USER>/base64/${params.recordDetailAlarmId}`,
    param: params,
  });
}

/**
* 排名统计
*
* url path: /v1/location/home/<USER>/{recordId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeRankRecordId(params: APIModels["V1LocationHomeRankRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeRankRecordIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.recordId}`,
    param: params,
  });
}

/**
* 所有排名
*
* url path: /v1/location/home/<USER>/{projectId}/{type}/{start}/{end}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeRankAllProjectIdTypeStartEnd(params: APIModels["V1LocationHomeRankAllProjectIdTypeStartEndGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeRankAllProjectIdTypeStartEndGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.projectId}/${params.type}/${params.start}/${params.end}`,
    param: params,
  });
}

/**
* 训练进度统计查询接口
*
* url path: /v1/location/home/<USER>/{recordId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeProgressStatisticsRecordId(params: APIModels["V1LocationHomeProgressStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeProgressStatisticsRecordIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.recordId}`,
    param: params,
  });
}

/**
* 我的项目查询接口
*
* url path: /v1/location/home/<USER>/{locationId}/{userId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeMyProjectLocationIdUserId(query: APIModels["V1LocationHomeMyProjectLocationIdUserIdGetQuery"], params: APIModels["V1LocationHomeMyProjectLocationIdUserIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeMyProjectLocationIdUserIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.locationId}/${params.userId}`,
    query: query,
    param: params,
  });
}

/**
* 小程序-我的数据统计
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeMine() {
  return httpAdaptor.get<APIModels["V1LocationHomeMineGetResponse"]>({
    url: `/v1/location/home/<USER>
  });
}

/**
* 工位端登录同步
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeLoginSync() {
  return httpAdaptor.get<APIModels["V1LocationHomeLoginSyncGetResponse"]>({
    url: `/v1/location/home/<USER>
  });
}

/**
* 最新训练数据查询接口
*
* url path: /v1/location/home/<USER>/{recordId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeLastStatisticsRecordId(params: APIModels["V1LocationHomeLastStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeLastStatisticsRecordIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.recordId}`,
    param: params,
  });
}

/**
* 数据统计查询接口
*
* url path: /v1/location/home/<USER>/{recordId}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeDataStatisticsRecordId(params: APIModels["V1LocationHomeDataStatisticsRecordIdGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeDataStatisticsRecordIdGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.recordId}`,
    param: params,
  });
}

/**
* 当前登录学员查询接口
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeCurrentUser() {
  return httpAdaptor.get<APIModels["V1LocationHomeCurrentUserGetResponse"]>({
    url: `/v1/location/home/<USER>
  });
}

/**
* 当前工位查询接口
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeCurrentLocation(query: APIModels["V1LocationHomeCurrentLocationGetQuery"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeCurrentLocationGetResponse"]>({
    url: `/v1/location/home/<USER>
    query: query,
  });
}

/**
* 当前工位查询接口
*
* url path: /v1/location/home/<USER>/{ip}
* @tags 工位端-home页控制器
*/
export function V1LocationHomeCurrentLocationIp(params: APIModels["V1LocationHomeCurrentLocationIpGetParam"]) {
  return httpAdaptor.get<APIModels["V1LocationHomeCurrentLocationIpGetResponse"]>({
    url: `/v1/location/home/<USER>/${params.ip}`,
    param: params,
  });
}

/**
* 当前登录IP查询接口
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeCurrentIp() {
  return httpAdaptor.get<APIModels["V1LocationHomeCurrentIpGetResponse"]>({
    url: `/v1/location/home/<USER>
  });
}

/**
* 当前时间查询接口
*
* url path: /v1/location/home/<USER>
* @tags 工位端-home页控制器
*/
export function V1LocationHomeCurrentDate() {
  return httpAdaptor.get<APIModels["V1LocationHomeCurrentDateGetResponse"]>({
    url: `/v1/location/home/<USER>
  });
}

/**
* 评定项目下拉框查询接口
*
* url path: /v1/evaluate/config/select
* @tags 评定项目配置
*/
export function V1EvaluateConfigSelect() {
  return httpAdaptor.get<APIModels["V1EvaluateConfigSelectGetResponse"]>({
    url: `/v1/evaluate/config/select`,
  });
}

/**
* 人脸采集接口
*
* url path: /device/captureFaceInfo
* @tags 刷卡机
*/
export function DeviceCaptureFaceInfo(query: APIModels["DeviceCaptureFaceInfoGetQuery"]) {
  return httpAdaptor.get<APIModels["DeviceCaptureFaceInfoGetResponse"]>({
    url: `/device/captureFaceInfo`,
    query: query,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/tran-project/delete/{id}
* @tags 管理端-项目课程管理
*/
export function V1ManageTranProjectDeleteIdDelete(params: APIModels["V1ManageTranProjectDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageTranProjectDeleteIdDeleteResponse"]>({
    url: `/v1/manage/tran-project/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/sys-user/delete/{id}
* @tags 管理端-用户管理(sys-user)
*/
export function V1ManageSysUserDeleteIdDelete(params: APIModels["V1ManageSysUserDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageSysUserDeleteIdDeleteResponse"]>({
    url: `/v1/manage/sys-user/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/sys-unit/delete/{id}
* @tags 管理端-组织机构(工位)管理
*/
export function V1ManageSysUnitDeleteIdDelete(params: APIModels["V1ManageSysUnitDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageSysUnitDeleteIdDeleteResponse"]>({
    url: `/v1/manage/sys-unit/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/sys-org/delete/{id}
* @tags 管理端-组织机构管理
*/
export function V1ManageSysOrgDeleteIdDelete(params: APIModels["V1ManageSysOrgDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageSysOrgDeleteIdDeleteResponse"]>({
    url: `/v1/manage/sys-org/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/process-algorithm/{id}
* @tags 管理端-工艺算法管理
*/
export function V1ManageProcessAlgorithmIdDelete(params: APIModels["V1ManageProcessAlgorithmIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageProcessAlgorithmIdDeleteResponse"]>({
    url: `/v1/manage/process-algorithm/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/manage/exam-paper-question/{id}
* @tags 管理端-试卷试题管理
*/
export function V1ManageExamPaperQuestionIdDelete(params: APIModels["V1ManageExamPaperQuestionIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1ManageExamPaperQuestionIdDeleteResponse"]>({
    url: `/v1/manage/exam-paper-question/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/evaluate/task/delete/{id}
* @tags 评定任务
*/
export function V1EvaluateTaskDeleteIdDelete(params: APIModels["V1EvaluateTaskDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1EvaluateTaskDeleteIdDeleteResponse"]>({
    url: `/v1/evaluate/task/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除接口
*
* url path: /v1/evaluate/config/delete/{id}
* @tags 评定项目配置
*/
export function V1EvaluateConfigDeleteIdDelete(params: APIModels["V1EvaluateConfigDeleteIdDeleteParam"]) {
  return httpAdaptor.delete<APIModels["V1EvaluateConfigDeleteIdDeleteResponse"]>({
    url: `/v1/evaluate/config/delete/${params.id}`,
    param: params,
  });
}

/**
* 删除人员接口
*
* url path: /device/deleteAllEmployeeInfo
* @tags 刷卡机
*/
export function DeviceDeleteAllEmployeeInfoDelete() {
  return httpAdaptor.delete<APIModels["DeviceDeleteAllEmployeeInfoDeleteResponse"]>({
    url: `/device/deleteAllEmployeeInfo`,
  });
}