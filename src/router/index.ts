import { createRouter, createWebHashHistory } from 'vue-router'
import { handleHotUpdate, routes } from 'vue-router/auto-routes'
import { createMetaGuard } from './meta'
import { createPermissionGuard } from './permission'

export const history = createWebHashHistory()

export const router = createRouter({
  history,
  routes,
})

createPermissionGuard(router)
createMetaGuard(router)

if (import.meta.hot) {
  handleHotUpdate(router)
}
