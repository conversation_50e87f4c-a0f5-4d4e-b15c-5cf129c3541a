<script lang="ts" setup>
import { toggleDevtools } from '@/integration/vconsole'


let count = 0

let handle = 0

function onClick() {
  clearTimeout(handle)

  count++

  if (count >= 10) {
    toggleDevtools()
    count = 0
  }

  handle = window.setTimeout(() => {
    count = 0
  }, 1000)
}
</script>

<template>
  <div @click="onClick">
    <slot></slot>
  </div>
</template>

<style lang="less" scoped></style>
