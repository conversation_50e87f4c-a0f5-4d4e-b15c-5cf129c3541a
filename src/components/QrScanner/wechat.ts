import type { WxConfig } from '@/utils/wechat'
/**
 * 微信相关API接口
 */
import { V1OpenApiUserLoginWechatSignaturePost } from '@/api/api.req'
import { DEFAULT_JS_API_LIST } from '@/config/wechat'
import { wechatSignatureManager } from '@/utils/wechatSignature'

// 获取微信JS-SDK配置的请求参数
export interface GetWxConfigParams {
  url: string // 当前页面的URL，不包含#及其后面部分
}

// 获取微信JS-SDK配置的响应数据
export interface GetWxConfigResponse {
  appId: string
  timestamp: string
  nonceStr: string
  signature: string
  jsApiList: string[]
}

/**
 * 获取微信JS-SDK配置信息（带缓存）
 * @param params 请求参数
 * @returns 微信配置信息
 */
export async function getWxConfig(params: GetWxConfigParams): Promise<GetWxConfigResponse> {
  try {
    // 先检查本地缓存
    const cachedSignature = wechatSignatureManager.getCachedSignature(params.url)

    // 如果缓存存在且未过期，直接使用缓存
    if (cachedSignature && !wechatSignatureManager.shouldRefreshSignature(params.url)) {
      console.log('使用缓存的微信签名配置')
      return {
        appId: cachedSignature.appId,
        timestamp: cachedSignature.timestamp,
        nonceStr: cachedSignature.nonceStr,
        signature: cachedSignature.signature,
        jsApiList: DEFAULT_JS_API_LIST,
      }
    }

    // 缓存不存在或需要刷新，从服务器获取新签名
    console.log('从服务器获取新的微信签名')
    const response = await V1OpenApiUserLoginWechatSignaturePost({ url: params.url })

    if (!response) {
      throw new Error('获取微信配置失败：响应数据为空')
    }

    const configData = {
      appId: response.appid || '',
      timestamp: response.timestamp || '',
      nonceStr: response.nonceStr || '',
      signature: response.signature || '',
      jsApiList: DEFAULT_JS_API_LIST,
    }

    // 缓存新获取的签名
    wechatSignatureManager.cacheSignature(params.url, {
      appId: configData.appId,
      timestamp: configData.timestamp,
      nonceStr: configData.nonceStr,
      signature: configData.signature,
    })

    return configData
  }
  catch (error) {
    console.error('获取微信配置失败:', error)
    throw new Error('获取微信配置失败，请稍后重试')
  }
}

/**
 * 获取当前页面URL（用于微信JS-SDK配置）
 * @returns 当前页面URL
 */
export function getCurrentPageUrl(): string {
  // 获取当前页面的完整URL，但不包含hash部分
  const url = window.location.href.split('#')[0]
  return url
}

/**
 * 构建微信JS-SDK配置对象
 * @param configData 从后端获取的配置数据
 * @param debug 是否开启调试模式
 * @returns 微信配置对象
 */
export function buildWxConfig(configData: GetWxConfigResponse, debug = false): WxConfig {
  return {
    debug,
    appId: configData.appId, // 使用 appId 作为 appId
    timestamp: Number.parseInt(configData.timestamp) || Date.now(), // 转换为数字类型
    nonceStr: configData.nonceStr,
    signature: configData.signature,
    jsApiList: configData.jsApiList || DEFAULT_JS_API_LIST,
  }
}

/**
 * 初始化微信JS-SDK
 * @param debug 是否开启调试模式
 * @returns Promise<WxConfig>
 */
export async function initWechatSDK(debug = false): Promise<WxConfig> {
  try {
    // 清理过期的签名缓存
    wechatSignatureManager.cleanupExpiredSignatures()

    // 获取当前页面URL
    const url = getCurrentPageUrl()

    // 从后端获取微信配置（带缓存）
    const configData = await getWxConfig({ url })

    // 构建配置对象
    const wxConfig = buildWxConfig(configData, debug)

    console.log('微信SDK配置完成', {
      appId: wxConfig.appId,
      url,
      debug,
      remainingTime: `${wechatSignatureManager.getRemainingTime(url)}分钟`,
    })

    return wxConfig
  }
  catch (error) {
    console.error('初始化微信SDK失败:', error)
    throw error
  }
}
