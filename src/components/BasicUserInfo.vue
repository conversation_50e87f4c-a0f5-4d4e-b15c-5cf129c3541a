<script lang="ts" setup>
import { currentUserInfo } from '@/store/sysUser'

export interface BasicUserInfoProps {
  showJobTag?: boolean
}

defineProps<BasicUserInfoProps>()
</script>

<template>
  <CardBox class="p-0!">
    <div class="avatar-info flex pt-4 px-4">
      <div class="avatar mr-4 mt-3">
        <img class="size-full" src="@/assets/guc-avatar.svg" />
      </div>
      <div class="flex flex-col">
        <div class="mb-2 flex items-center gap-1 h-24px">
          <span class="name font-bold">
            {{ currentUserInfo.name }}
          </span>
          <ColorTag color="gray" v-if="showJobTag && currentUserInfo.jobName">
            {{ currentUserInfo.jobName }} L{{ currentUserInfo.level || 0 }}
          </ColorTag>
        </div>
        <div class="flex gap-1">
          <ColorTag color="green" shape="round" border>
            {{ currentUserInfo.orgList?.map((n) => n.name).join('/') || `无组织` }}
          </ColorTag>
        </div>
        <div class="my-5">
          <QrScanner
            button-text="扫一扫"
            button-type="primary"
            button-size="small"
            button-class=""
          />
        </div>
      </div>
    </div>
    <slot />
  </CardBox>
</template>

<style lang="less" scoped>
.avatar {
  @size: 64px;
  width: @size;
  height: @size;
  border-radius: 999px;
}

.avatar-info {
  background:
    linear-gradient(339deg, rgba(255, 255, 255, 0) 68.39%, #fff 82.74%),
    linear-gradient(278deg, rgba(255, 255, 255, 0) 8.95%, #fff 98.4%),
    url('@/assets/home-bg.jpg') lightgray left -40px / 100% auto no-repeat;
}

.level-tag {
  display: inline-flex;
  padding: 2px 6px;
  justify-content: center;
  align-items: center;
  gap: 2px;

  border-radius: 3px;
  background: #e1e1e1;
}
</style>
